#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试鼠标操作修复
验证鼠标点击功能是否正常工作
"""

import sys
import os
import time
import pyautogui
import logging

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_mouse_visual_feedback():
    """测试鼠标视觉反馈模块"""
    logger.info("🧪 测试鼠标视觉反馈模块...")
    
    try:
        from modules.mouse_visual_feedback import MouseVisualFeedback
        
        # 创建实例
        mouse_feedback = MouseVisualFeedback()
        logger.info("✅ 鼠标视觉反馈模块创建成功")
        
        # 测试移动功能
        start_pos = (100, 100)
        end_pos = (200, 200)
        
        result = mouse_feedback.enhanced_move_to(start_pos, end_pos, 0.5, "测试移动")
        if result:
            logger.info("✅ 鼠标移动测试成功")
        else:
            logger.error("❌ 鼠标移动测试失败")
            
        # 测试点击效果
        mouse_feedback.show_click_effect((150, 150), "测试点击")
        logger.info("✅ 点击效果测试完成")
        
        # 测试清理
        mouse_feedback.cleanup_all()
        logger.info("✅ 清理测试完成")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 鼠标视觉反馈模块测试失败: {e}")
        return False

def test_main_interface():
    """测试主界面模块"""
    logger.info("🧪 测试主界面模块...")
    
    try:
        from modules.main_interface import WeChatMainInterface
        
        # 创建实例
        main_interface = WeChatMainInterface()
        logger.info("✅ 主界面模块创建成功")
        
        # 测试安全点击功能
        result = main_interface._safe_click(100, 100, "测试点击", 0.1)
        if result:
            logger.info("✅ 安全点击测试成功")
        else:
            logger.error("❌ 安全点击测试失败")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ 主界面模块测试失败: {e}")
        return False

def test_basic_mouse_operations():
    """测试基础鼠标操作"""
    logger.info("🧪 测试基础鼠标操作...")
    
    try:
        # 获取当前鼠标位置
        current_pos = pyautogui.position()
        logger.info(f"当前鼠标位置: {current_pos}")
        
        # 测试移动
        test_x, test_y = current_pos.x + 50, current_pos.y + 50
        pyautogui.moveTo(test_x, test_y, duration=0.5)
        logger.info(f"✅ 鼠标移动到: ({test_x}, {test_y})")
        
        # 移动回原位置
        pyautogui.moveTo(current_pos.x, current_pos.y, duration=0.5)
        logger.info("✅ 鼠标移动回原位置")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 基础鼠标操作测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始鼠标操作修复测试...")
    
    # 禁用PyAutoGUI安全特性
    pyautogui.FAILSAFE = False
    pyautogui.PAUSE = 0.1
    
    test_results = []
    
    # 测试1: 基础鼠标操作
    logger.info("\n" + "="*50)
    result1 = test_basic_mouse_operations()
    test_results.append(("基础鼠标操作", result1))
    
    # 测试2: 鼠标视觉反馈模块
    logger.info("\n" + "="*50)
    result2 = test_mouse_visual_feedback()
    test_results.append(("鼠标视觉反馈模块", result2))
    
    # 测试3: 主界面模块
    logger.info("\n" + "="*50)
    result3 = test_main_interface()
    test_results.append(("主界面模块", result3))
    
    # 输出测试结果
    logger.info("\n" + "="*50)
    logger.info("📊 测试结果汇总:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        logger.info("\n🎉 所有测试通过！鼠标操作修复成功！")
        logger.info("💡 鼠标现在应该能够正常点击，没有视觉效果干扰。")
    else:
        logger.error("\n⚠️ 部分测试失败，请检查相关模块。")
    
    return all_passed

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("\n⏹ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"\n💥 测试过程中发生未预期错误: {e}")
        sys.exit(1)
