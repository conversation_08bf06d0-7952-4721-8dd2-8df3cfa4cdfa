2025-08-01 15:32:55,867 - __main__ - INFO - 🚀 开始动态参数响应功能测试
2025-08-01 15:32:55,868 - __main__ - INFO - ============================================================
2025-08-01 15:32:55,868 - __main__ - INFO - 🧪 测试1：实时参数读取功能
2025-08-01 15:32:55,869 - __main__ - INFO - ✅ 创建测试配置：每日限制=10，单窗口限制=5
2025-08-01 15:32:55,871 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250801_153255.log
2025-08-01 15:32:55,872 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 15:32:55,872 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-08-01 15:32:55,872 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-08-01 15:32:55,873 - __main__ - INFO - 📊 第一次读取：每日限制=10，单窗口限制=2
2025-08-01 15:32:56,876 - __main__ - INFO - ✅ 创建测试配置：每日限制=15，单窗口限制=8
2025-08-01 15:33:02,886 - __main__ - INFO - 📊 第二次读取：每日限制=10，单窗口限制=2
2025-08-01 15:33:02,886 - __main__ - ERROR - ❌ 测试1失败：参数更新不正确
2025-08-01 15:33:02,887 - __main__ - INFO - ----------------------------------------
2025-08-01 15:33:02,887 - __main__ - INFO - 🧪 测试2：参数缓存机制
2025-08-01 15:33:02,888 - __main__ - INFO - ✅ 创建测试配置：每日限制=20，单窗口限制=10
2025-08-01 15:33:02,890 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250801_153302.log
2025-08-01 15:33:02,891 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 15:33:02,892 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-08-01 15:33:02,892 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-08-01 15:33:02,894 - __main__ - INFO - 📊 第一次读取耗时：0.0007秒
2025-08-01 15:33:02,894 - __main__ - INFO - 📊 第二次读取耗时：0.0000秒
2025-08-01 15:33:02,895 - __main__ - INFO - ✅ 测试2通过：参数缓存机制正常
2025-08-01 15:33:02,895 - __main__ - INFO - ----------------------------------------
2025-08-01 15:33:02,895 - __main__ - INFO - 🧪 测试3：配置文件同步
2025-08-01 15:33:02,898 - __main__ - INFO - ✅ 创建测试配置：每日限制=25，单窗口限制=12
2025-08-01 15:33:02,921 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 15:33:02,922 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 15:33:02,923 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 15:33:02,926 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-08-01 15:33:02,927 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-08-01 15:33:02,929 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 有效微信窗口统计: 0 个
2025-08-01 15:33:02,929 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 0 个微信窗口
2025-08-01 15:33:02,931 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 15:33:02,936 - modules.main_interface - INFO - ✅ 配置文件加载成功: test_config.json
2025-08-01 15:33:02,937 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-08-01 15:33:02,938 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 15:33:02,942 - modules.friend_request_window - INFO - ✅ 已加载配置文件: test_config.json
2025-08-01 15:33:02,943 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-08-01 15:33:02,943 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-08-01 15:33:02,944 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-08-01 15:33:02,944 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-08-01 15:33:02,944 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-08-01 15:33:02,945 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-08-01 15:33:02,945 - main_controller - INFO - 🧠 启用智能检测功能...
2025-08-01 15:33:02,945 - main_controller - INFO - ✅ 智能检测功能已启用
2025-08-01 15:33:02,946 - main_controller - INFO - 🔍 支持的检测方法:
2025-08-01 15:33:02,946 - main_controller - INFO - 🖱️ 支持的点击方法:
2025-08-01 15:33:02,948 - main_controller - INFO - 🔄 重试设置: 检测最多3次, 点击最多4次
2025-08-01 15:33:02,953 - main_controller - INFO - 📱 已启用跨分辨率兼容性
2025-08-01 15:33:02,954 - main_controller - INFO - 🪟 已启用窗口位置无关性
2025-08-01 15:33:02,954 - main_controller - INFO - ✅ 微信自动化主控制器初始化完成
2025-08-01 15:33:02,954 - main_controller - INFO - 📅 当前北京时间: 2025-08-01 23:33:02
2025-08-01 15:33:02,955 - main_controller - WARNING - ⚠️ 未找到时间段配置，将使用默认设置
2025-08-01 15:33:02,957 - main_controller - WARNING - ⚠️ 主控制器实时读取运行参数失败: name 'json' is not defined，使用缓存或默认值
2025-08-01 15:33:02,957 - __main__ - INFO - 📊 主控制器读取：每日限制=None，单窗口限制=None
2025-08-01 15:33:02,958 - __main__ - ERROR - ❌ 测试3失败：配置文件同步异常
2025-08-01 15:33:02,958 - __main__ - INFO - ----------------------------------------
2025-08-01 15:33:02,958 - __main__ - INFO - 🧪 测试4：数量限制强制执行
2025-08-01 15:33:02,960 - __main__ - INFO - ✅ 创建测试配置：每日限制=3，单窗口限制=2
2025-08-01 15:33:02,961 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250801_153302.log
2025-08-01 15:33:02,962 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 15:33:02,964 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-08-01 15:33:02,968 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-08-01 15:33:02,976 - __main__ - INFO - 📊 限制配置：每日限制=10，单窗口限制=2
2025-08-01 15:33:02,977 - __main__ - ERROR - ❌ 测试4失败：数量限制配置错误
2025-08-01 15:33:02,978 - __main__ - INFO - ----------------------------------------
2025-08-01 15:33:02,979 - __main__ - INFO - 📊 测试结果汇总：
2025-08-01 15:33:02,980 - __main__ - INFO - ============================================================
2025-08-01 15:33:02,983 - __main__ - INFO - ❌ 失败 实时参数读取: 期望更新但实际值：10->10, 2->2
2025-08-01 15:33:02,985 - __main__ - INFO - ✅ 通过 参数缓存机制: 缓存读取提速99.3%
2025-08-01 15:33:02,986 - __main__ - INFO - ❌ 失败 配置文件同步: 期望25/12，实际None/None
2025-08-01 15:33:02,987 - __main__ - INFO - ❌ 失败 数量限制强制执行: 期望3/2，实际10/2
2025-08-01 15:33:02,987 - __main__ - INFO - ============================================================
2025-08-01 15:33:02,988 - __main__ - INFO - 🎯 测试完成：1/4 个测试通过
2025-08-01 15:33:02,988 - __main__ - WARNING - ⚠️ 3 个测试失败，需要检查相关功能
2025-08-01 15:33:02,990 - __main__ - INFO - 🧹 测试配置文件已清理
