#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
申请添加朋友窗口自动化处理工具
功能包括：
1. 窗口检测和激活功能
2. 验证信息填写和提交
3. 批量处理Excel中的联系人
4. 错误处理和自动重试
5. 详细日志记录和结果统计

使用固定坐标：
- 验证信息输入框: (960, 330)
- 备注信息输入框: (960, 450)
- 确定按钮: (910, 840)

修复内容：
1. 正确读取Excel表格中的"验证信息"列数据
2. 按照"准考证号-姓名-北京时间"格式生成备注信息
3. 验证必要字段：手机号、验证信息、备注信息、准考证号、姓名
4. 详细的日志记录显示实际填写内容
"""

import sys
import time
import logging
import traceback
import argparse
import json
import importlib.util
from pathlib import Path
from datetime import datetime, timezone, timedelta
from typing import Dict, Optional, Union, Any
import random
import pyautogui
import win32gui
import win32con
import pandas as pd

# 导入剪贴板模块（修复：在顶部导入避免运行时导入延迟）
try:
    import pyperclip
    PYPERCLIP_AVAILABLE = True
except ImportError:
    PYPERCLIP_AVAILABLE = False
    print("⚠️ 警告：pyperclip未安装，将使用直接输入方法")

class FriendRequestProcessor:
    """微信好友申请窗口自动化处理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """初始化处理器"""
        # 使用系统统一的日志配置，不创建新的日志文件
        self.logger = logging.getLogger(__name__)

        # 加载配置
        self.config = self.load_config(config_file)
        
        # 固定坐标配置
        self.coordinates = {
            "验证信息输入框": (960, 330),
            "备注信息输入框": (960, 450),
            "确定按钮": (910, 840)
        }
        
        self.logger.info("🔧 使用固定坐标配置:")
        for element, coords in self.coordinates.items():
            self.logger.info(f"   📍 {element}: {coords}")
        
        # 默认数据
        self.default_data = self.config.get("default_data", {
            "verification": "您好，我想加您为好友",
            "remark": "新朋友+{phone}+{date}"
        })
        
        # Excel文件配置
        self.excel_file = self.config.get("excel_file", "添加好友名单.xlsx")
        
        # 操作配置
        self.step_delay = self.config.get("step_delay", 0.5)
        
        # 处理统计
        self.stats = {
            "total": 0,
            "success": 0,
            "failed": 0,
            "skipped": 0,
            "start_time": datetime.now(),
            "end_time": None,
            "details": []
        }
        
        # 重试配置
        self.max_retries = self.config.get("max_retries", 3)
        self.retry_delay = self.config.get("retry_delay", 2)
        
        # 安全配置
        self.operation_interval = self.config.get("operation_interval", [1, 3])
        self.max_operations_per_hour = self.config.get("max_operations_per_hour", 30)
        self.cooldown_time = self.config.get("cooldown_time", 300)
        
        self.logger.info("✅ 微信好友申请窗口处理器初始化完成")
    
    def load_config(self, config_file: Optional[str] = None) -> Dict:
        """加载配置文件"""
        default_config = {
            "excel_file": "添加好友名单.xlsx",
            "log_file": "friend_request_processor.log",
            "step_delay": 0.5,
            "max_retries": 3,
            "retry_delay": 2,
            "operation_interval": [1, 3],
            "max_operations_per_hour": 30,
            "cooldown_time": 300,
            "debug_mode": False
        }
        
        if config_file and Path(config_file).exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    config = {**default_config, **user_config}
                    self.logger.info(f"✅ 已加载配置文件: {config_file}")
                    return config
            except Exception as e:
                self.logger.error(f"❌ 加载配置文件失败: {e}")
        
        self.logger.info("✅ 使用默认配置")
        return default_config
    

    
    def process_all_contacts(self) -> Dict:
        """处理Excel中的所有联系人"""
        self.logger.info("🚀 开始处理所有联系人")
        
        try:
            # 加载Excel数据
            df = self.load_excel_data()
            if df is None or len(df) == 0:
                self.logger.error("❌ 没有找到有效的联系人数据")
                return self.generate_report()
            
            self.stats["total"] = len(df)
            self.logger.info(f"📊 共找到 {len(df)} 个联系人")
            
            # 处理每个联系人
            for index, row in df.iterrows():
                row_index = int(index) if isinstance(index, (int, float)) else 0

                # 安全检查：限制每小时操作次数
                if self.stats["success"] % self.max_operations_per_hour == 0 and self.stats["success"] > 0:
                    cooldown_seconds = self.cooldown_time
                    self.logger.info(f"⏳ 已达到每小时最大操作次数，休息 {cooldown_seconds} 秒")
                    time.sleep(cooldown_seconds)

                # 获取联系人信息
                contact_info = self.extract_contact_info(row)
                if not contact_info:
                    self.logger.warning(f"⚠️ 跳过第 {row_index + 1} 行：无效的联系人信息")
                    self.stats["skipped"] += 1
                    continue

                # 处理联系人
                self.logger.info(f"🔄 处理联系人 {row_index + 1}/{len(df)}: {contact_info.get('phone', 'Unknown')}")
                result = self.process_single_contact(contact_info)

                # 更新Excel
                self.update_excel_result(df, row_index, result)
                
                # 随机延迟，模拟人工操作
                delay = random.uniform(self.operation_interval[0], self.operation_interval[1])
                self.logger.info(f"⏱️ 等待 {delay:.1f} 秒后处理下一个联系人")
                time.sleep(delay)
            
            # 保存更新后的Excel
            self.save_excel_data(df)
            
            # 生成报告
            self.stats["end_time"] = datetime.now()
            return self.generate_report()
            
        except Exception as e:
            self.logger.error(f"❌ 处理联系人时出错: {e}")
            traceback.print_exc()
            self.stats["end_time"] = datetime.now()
            return self.generate_report()
    
    def load_excel_data(self) -> Optional[pd.DataFrame]:
        """加载Excel数据（修复版：保留准考证号前导零）"""
        try:
            if not Path(self.excel_file).exists():
                self.logger.error(f"❌ Excel文件不存在: {self.excel_file}")
                return None

            # 定义准考证号转换器，确保保留前导零
            def exam_id_converter(x):
                """准考证号转换器，确保保留前导零"""
                if pd.isna(x):
                    return ""
                # 转换为字符串并去除空格
                str_x = str(x).strip()
                # 如果是纯数字且长度不足12位，补充前导零到12位
                if str_x.isdigit() and len(str_x) < 12:
                    return str_x.zfill(12)
                return str_x

            # 使用converters确保准考证号格式正确
            # 类型忽略：pandas的converters类型定义过于严格
            converters = {"准考证": exam_id_converter}
            df = pd.read_excel(self.excel_file, converters=converters)  # type: ignore
            self.logger.info(f"✅ Excel文件加载成功，共 {len(df)} 行数据")
            self.logger.info("🔧 已应用准考证号前导零修复逻辑")
            
            # 验证必要字段（修复版：匹配实际Excel列名）
            required_fields = ["手机号码", "验证信息", "准考证", "姓名"]
            missing_fields = [field for field in required_fields if field not in df.columns]

            if missing_fields:
                self.logger.error(f"❌ Excel文件缺少必要字段: {missing_fields}")
                self.logger.error("📋 Excel表格必须包含以下列：手机号码、验证信息、准考证、姓名")
                return None
            
            self.logger.info("✅ Excel字段验证通过，包含所有必要字段")
            self.logger.info(f"📊 Excel列名: {list(df.columns)}")
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 加载Excel数据失败: {e}")
            return None

    def save_excel_data(self, df: pd.DataFrame):
        """保存Excel数据"""
        try:
            # 创建备份
            backup_file = f"{self.excel_file}.bak.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            if Path(self.excel_file).exists():
                df_original = pd.read_excel(self.excel_file)
                df_original.to_excel(backup_file, index=False)
                self.logger.info(f"✅ 已创建Excel备份: {backup_file}")

            # 保存更新后的数据
            df.to_excel(self.excel_file, index=False)
            self.logger.info(f"✅ 已保存更新后的Excel数据: {self.excel_file}")

        except Exception as e:
            self.logger.error(f"❌ 保存Excel数据失败: {e}")

    def extract_contact_info(self, row: pd.Series) -> Optional[Dict]:
        """从Excel行中提取联系人信息（修复版：匹配实际Excel列名）"""
        try:
            # 1. 提取手机号（修复：使用"手机号码"而不是"手机号"）
            phone = None
            if "手机号码" in row.index and pd.notna(row["手机号码"]) and str(row["手机号码"]).strip():
                phone = str(row["手机号码"]).strip()

            if not phone:
                self.logger.warning("⚠️ 手机号码为空，跳过该联系人")
                return None

            # 2. 提取准考证号（修复：使用"准考证"而不是"准考证号"）
            exam_id = None
            if "准考证" in row.index and pd.notna(row["准考证"]) and str(row["准考证"]).strip():
                exam_id = str(row["准考证"]).strip()

            if not exam_id:
                self.logger.warning(f"⚠️ 手机号 {phone} 的准考证为空，跳过该联系人")
                return None

            # 3. 提取姓名
            name = None
            if "姓名" in row.index and pd.notna(row["姓名"]) and str(row["姓名"]).strip():
                name = str(row["姓名"]).strip()

            if not name:
                self.logger.warning(f"⚠️ 手机号 {phone} 的姓名为空，跳过该联系人")
                return None

            # 4. 提取验证信息（修复：严格按照Excel中的验证信息填写）
            verification = None
            if "验证信息" in row.index and pd.notna(row["验证信息"]):
                verification = str(row["验证信息"]).strip()
                if verification:  # 确保验证信息不为空
                    self.logger.info(f"📝 使用Excel中的验证信息: '{verification}'")
                else:
                    self.logger.error(f"❌ 手机号 {phone} 的验证信息为空，跳过该联系人")
                    return None
            else:
                self.logger.error(f"❌ 手机号 {phone} 缺少验证信息列或验证信息为空，跳过该联系人")
                return None

            # 5. 生成备注信息（格式：准考证号-姓名-北京时间）
            # 修复：使用完整的北京时间格式，包含年月日时分秒，使用正确的北京时区
            beijing_tz = timezone(timedelta(hours=8))
            beijing_time = datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')
            remark = f"{exam_id}-{name}-{beijing_time}"

            contact_info = {
                "phone": phone,
                "verification": verification,
                "remark": remark,
                "exam_id": exam_id,
                "name": name,
                "beijing_time": beijing_time
            }

            # 🔧 修复：添加数据校验，确保信息准确性
            if not self._validate_contact_data(contact_info):
                self.logger.error(f"❌ 联系人数据验证失败: {phone}")
                return None

            # 记录详细信息到日志
            self.logger.info(f"📋 联系人信息提取完成:")
            self.logger.info(f"   📱 手机号码: {phone}")
            self.logger.info(f"   🆔 准考证: {exam_id}")
            self.logger.info(f"   👤 姓名: {name}")
            self.logger.info(f"   💬 验证信息: '{verification}'")
            self.logger.info(f"   📝 备注格式: '{remark}'")

            return contact_info

        except Exception as e:
            self.logger.error(f"❌ 提取联系人信息失败: {e}")
            traceback.print_exc()
            return None

    def update_excel_result(self, df: pd.DataFrame, index: int, result: Dict):
        """更新Excel中的处理结果"""
        try:
            # 更新添加状态
            if "添加状态" not in df.columns:
                df["添加状态"] = ""
            df.at[index, "添加状态"] = result.get("status", "未知")

            # 更新添加时间
            if "添加时间" not in df.columns:
                df["添加时间"] = ""
            df.at[index, "添加时间"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 更新失败原因
            if "失败原因" not in df.columns:
                df["失败原因"] = ""
            if result.get("status") != "成功":
                df.at[index, "失败原因"] = result.get("error", "")

        except Exception as e:
            self.logger.error(f"❌ 更新Excel结果失败: {e}")

    def process_single_contact(self, contact_info: Dict) -> Dict:
        """处理单个联系人"""
        result = {
            "phone": contact_info.get("phone", "Unknown"),
            "status": "失败",
            "error": "",
            "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        retry_count = 0
        success = False

        while retry_count <= self.max_retries and not success:
            if retry_count > 0:
                self.logger.info(f"🔄 第 {retry_count} 次重试...")
                time.sleep(self.retry_delay)

            try:
                # 步骤1: 窗口检测
                window = self._detect_friend_request_window()
                if not window:
                    result["error"] = "未找到申请添加朋友窗口"
                    self.logger.warning(f"⚠️ {result['error']}")
                    retry_count += 1
                    continue

                # 步骤2: 窗口激活
                if not self._activate_window(window):
                    result["error"] = "窗口激活失败"
                    self.logger.warning(f"⚠️ {result['error']}")
                    retry_count += 1
                    continue

                # 步骤3: 填写信息并提交
                verification = contact_info.get("verification", "")
                remark = contact_info.get("remark", "")

                # 记录即将填写的信息
                self.logger.info("📝 准备填写信息:")
                self.logger.info(f"   📱 手机号码: {contact_info.get('phone', 'Unknown')}")
                self.logger.info(f"   🆔 准考证: {contact_info.get('exam_id', 'Unknown')}")
                self.logger.info(f"   👤 姓名: {contact_info.get('name', 'Unknown')}")
                self.logger.info(f"   💬 验证信息: '{verification}'")
                self.logger.info(f"   📝 备注信息: '{remark}'")
                self.logger.info(f"   🕐 北京时间: {contact_info.get('beijing_time', 'Unknown')}")

                if not self._fill_and_submit_information(verification or "", remark or ""):
                    result["error"] = "信息填写或提交失败"
                    self.logger.warning(f"⚠️ {result['error']}")
                    retry_count += 1
                    continue

                # 成功处理（信息填写完成并已点击确定按钮）
                success = True
                result["status"] = "成功"
                self.stats["success"] += 1
                self.logger.info(f"✅ 成功处理联系人:")
                self.logger.info(f"   📱 手机号码: {contact_info.get('phone')}")
                self.logger.info(f"   🆔 准考证: {contact_info.get('exam_id')}")
                self.logger.info(f"   👤 姓名: {contact_info.get('name')}")
                self.logger.info(f"   💬 已填写验证信息: '{verification}'")
                self.logger.info(f"   📝 已填写备注信息: '{remark}'")
                self.logger.info(f"   ✅ 已点击确定按钮提交申请")

            except Exception as e:
                result["error"] = f"处理异常: {str(e)}"
                self.logger.error(f"❌ 处理联系人时出错: {e}")
                traceback.print_exc()
                retry_count += 1

        # 如果所有重试都失败
        if not success:
            self.stats["failed"] += 1
            self.logger.error(f"❌ 处理联系人失败，已达到最大重试次数: {contact_info.get('phone')}")

        # 记录详细结果
        self.stats["details"].append(result)
        return result

    def _detect_friend_request_window(self) -> Optional[int]:
        """检测微信好友申请窗口（增强版）"""
        self.logger.info("🔍 检测微信好友申请窗口")

        def callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd) and win32gui.IsWindowEnabled(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                class_name = win32gui.GetClassName(hwnd)

                # 确保 class_name 不为 None，提供默认值
                if class_name is None:
                    class_name = ""

                # 注释：移除了模糊匹配规则，现在使用精确匹配避免窗口混淆

                # 窗口大小过滤（添加朋友窗口通常是小窗口）
                try:
                    rect = win32gui.GetWindowRect(hwnd)
                    width = rect[2] - rect[0]
                    height = rect[3] - rect[1]
                    size_appropriate = (200 <= width <= 500 and 300 <= height <= 700)
                except:
                    size_appropriate = True  # 如果获取大小失败，不过滤

                # 严格的综合判断 - 确保只匹配"申请添加朋友"窗口
                title_exact_match = window_text == "申请添加朋友"
                class_exact_match = class_name == "Qt51514QWindowIcon"

                # 必须同时满足：精确标题匹配 + 精确类名匹配 + 合适大小
                if title_exact_match and class_exact_match and size_appropriate:
                    # 额外验证：确保不是"添加朋友"窗口
                    if window_text != "添加朋友":
                        windows.append({
                            'hwnd': hwnd,
                            'title': window_text,
                            'class_name': class_name,
                            'width': width,
                            'height': height,
                            'match_reason': self._get_match_reason(window_text, class_name)
                        })
                        self.logger.info(f"✅ 找到精确匹配的申请添加朋友窗口: '{window_text}' (句柄: {hwnd}, 类名: {class_name}, 大小: {width}x{height})")
                    else:
                        self.logger.debug(f"🚫 跳过'添加朋友'窗口，避免混淆: '{window_text}' (句柄: {hwnd})")
                else:
                    # 记录不匹配的原因（调试用）
                    if window_text and ("申请" in window_text or "添加" in window_text or "朋友" in window_text):
                        self.logger.debug(f"🔍 窗口不匹配: '{window_text}' (标题匹配: {title_exact_match}, 类名匹配: {class_exact_match}, 大小合适: {size_appropriate})")
            return True

        windows = []
        win32gui.EnumWindows(callback, windows)

        if not windows:
            self.logger.warning("⚠️ 未找到'申请添加朋友'窗口")
            return None

        # 按匹配优先级排序
        windows.sort(key=lambda x: self._calculate_match_priority(x))

        best_window = windows[0]
        window_hwnd = best_window['hwnd']

        self.logger.info(f"✅ 找到最佳匹配窗口: '{best_window['title']}' (句柄: {window_hwnd})")
        self.logger.info(f"   匹配原因: {best_window['match_reason']}")
        self.logger.info(f"   窗口大小: {best_window['width']}x{best_window['height']}")

        return window_hwnd

    def _get_match_reason(self, window_text: str, class_name: str) -> str:
        """获取匹配原因"""
        reasons = []

        if window_text == "申请添加朋友":
            reasons.append("精确标题匹配")
        elif "申请添加" in window_text:
            reasons.append("部分标题匹配")

        if class_name == "Qt51514QWindowIcon":
            reasons.append("微信Qt窗口类名")
        elif "Qt" in class_name:
            reasons.append("Qt框架窗口")

        return " + ".join(reasons)

    def _calculate_match_priority(self, window_info: dict) -> int:
        """计算匹配优先级（数值越小优先级越高）"""
        title = window_info['title']
        class_name = window_info['class_name']

        priority = 0

        # 标题优先级
        if title == "申请添加朋友":
            priority += 1  # 最高优先级

        # 类名优先级
        if class_name == "Qt51514QWindowIcon":
            priority += 0  # 微信标准窗口类名
        elif "Qt" in class_name:
            priority += 1
        else:
            priority += 2

        return priority

    def _activate_window(self, hwnd: int) -> bool:
        """激活窗口（简化版）"""
        try:
            self.logger.info(f"🔄 激活窗口: {hwnd}")

            # 检查窗口是否存在
            if not win32gui.IsWindow(hwnd):
                self.logger.error("❌ 窗口句柄无效")
                return False

            # 激活窗口
            if win32gui.IsIconic(hwnd):
                win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                time.sleep(0.3)

            win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
            time.sleep(0.2)
            win32gui.SetForegroundWindow(hwnd)
            time.sleep(0.3)
            win32gui.BringWindowToTop(hwnd)
            time.sleep(0.2)

            # 验证窗口是否成功激活
            active_hwnd = win32gui.GetForegroundWindow()
            if active_hwnd == hwnd:
                self.logger.info("✅ 窗口激活成功")
                return True
            else:
                self.logger.warning(f"⚠️ 窗口激活失败，当前活动窗口: {active_hwnd}")
                return False

        except Exception as e:
            self.logger.error(f"❌ 激活窗口时出错: {e}")
            return False

    def _fill_and_submit_information(self, verification: str, remark: str) -> bool:
        """填写信息并提交（完整版：包含点击确定按钮）"""
        try:
            self.logger.info("🔄 开始填写验证信息和备注")

            # 🔧 调试：添加详细的调用栈信息
            import traceback
            import inspect

            # 获取调用栈
            stack = traceback.extract_stack()
            self.logger.info("🔍 _fill_and_submit_information调用栈:")
            for i, frame in enumerate(stack[-5:-1]):  # 显示最近的4个调用
                self.logger.info(f"   {i+1}. {frame.filename}:{frame.lineno} in {frame.name}")
                self.logger.info(f"      {frame.line}")

            # 🔧 调试：详细记录传入的参数
            self.logger.info(f"🔍 _fill_and_submit_information接收到的参数:")
            self.logger.info(f"   📝 verification参数: '{verification}' (类型: {type(verification)}, 长度: {len(verification) if verification else 0})")
            self.logger.info(f"   📝 remark参数: '{remark}' (类型: {type(remark)}, 长度: {len(remark) if remark else 0})")

            # 🔧 修复：添加数据校验机制，确保填写内容的准确性
            if not verification or not verification.strip():
                self.logger.error("❌ 验证信息为空，无法填写")
                return False

            if not remark or not remark.strip():
                self.logger.error("❌ 备注信息为空，无法填写")
                return False

            # 验证备注信息格式（应该包含准考证号-姓名-时间）
            remark_parts = remark.split('-')
            if len(remark_parts) < 3:
                self.logger.warning(f"⚠️ 备注信息格式可能不正确: '{remark}'，期望格式：准考证号-姓名-北京时间")

            self.logger.info(f"📝 即将填写验证信息: '{verification}'")
            self.logger.info(f"📝 即将填写备注信息: '{remark}'")

            # 步骤1: 填写验证信息
            self.logger.info("📝 步骤1: 填写验证信息")
            if not self._click_and_fill("验证信息输入框", verification):
                self.logger.error(f"❌ 填写验证信息失败: '{verification}'")
                return False
            self.logger.info(f"✅ 验证信息填写成功: '{verification}'")

            # 步骤2: 填写备注信息
            self.logger.info("📝 步骤2: 填写备注信息")
            if not self._click_and_fill("备注信息输入框", remark):
                self.logger.error(f"❌ 填写备注信息失败: '{remark}'")
                return False
            self.logger.info(f"✅ 备注信息填写成功: '{remark}'")

            # 步骤3: 点击确定按钮提交申请
            self.logger.info("📝 步骤3: 点击确定按钮提交申请")
            self.logger.info("🎯 使用固定坐标配置:")
            self.logger.info("   验证信息输入框: (960, 330)")
            self.logger.info("   备注信息输入框: (960, 450)")
            self.logger.info("   确定按钮: (910, 840)")

            # 添加适当延迟，确保信息填写完成
            self.logger.info("⏱️ 等待0.8秒确保信息填写完成")
            time.sleep(0.8)

            # 点击确定按钮
            if not self._click_submit_button():
                self.logger.error("❌ 点击确定按钮失败")
                return False

            self.logger.info("✅ 所有信息填写完成，已点击确定按钮提交申请")
            self.logger.info(f"📋 最终提交内容确认:")
            self.logger.info(f"   💬 验证信息: '{verification}'")
            self.logger.info(f"   📝 备注信息: '{remark}'")
            time.sleep(0.5)
            return True

        except Exception as e:
            self.logger.error(f"❌ 填写信息时出错: {e}")
            traceback.print_exc()
            return False

    def _click_and_fill(self, element_name: str, text: str) -> bool:
        """点击元素并填写文本（增强版：修复输入稳定性和中文支持）"""
        if element_name not in self.coordinates:
            self.logger.error(f"❌ 未找到元素: {element_name}")
            return False

        x, y = self.coordinates[element_name]
        self.logger.info(f"🎯 准备填写 {element_name}")
        self.logger.info(f"   📍 坐标: ({x}, {y})")
        self.logger.info(f"   📝 内容: '{text}'")
        self.logger.info(f"   📏 内容长度: {len(text)} 字符")
        self.logger.info(f"   🔤 内容编码: {text.encode('utf-8') if text else 'None'}")

        # 最多重试3次
        max_retries = 3
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    self.logger.info(f"🔄 第 {attempt + 1} 次尝试填写 {element_name}")
                    time.sleep(1)  # 重试前等待更长时间

                # 步骤1: 点击输入框（增强版）
                self.logger.info(f"🖱️ 点击 {element_name}")
                pyautogui.click(x, y)
                time.sleep(0.8)  # 增加等待时间确保焦点切换

                # 步骤2: 清除现有内容（超强版）
                self.logger.info(f"🧹 清除 {element_name} 现有内容")

                # 第一轮清除：全选+删除
                pyautogui.hotkey('ctrl', 'a')
                time.sleep(0.3)
                pyautogui.press('delete')
                time.sleep(0.3)

                # 第二轮清除：全选+退格
                pyautogui.hotkey('ctrl', 'a')
                time.sleep(0.2)
                pyautogui.press('backspace')
                time.sleep(0.3)

                # 第三轮清除：多次退格（处理可能的残留字符）
                for _ in range(20):  # 最多删除20个字符
                    pyautogui.press('backspace')
                    time.sleep(0.05)

                # 第四轮清除：再次全选+删除
                pyautogui.hotkey('ctrl', 'a')
                time.sleep(0.2)
                pyautogui.press('delete')
                time.sleep(0.3)

                self.logger.info(f"✅ {element_name} 内容清除完成")

                # 步骤3: 输入文本（智能选择输入方法）
                if text:
                    # 智能判断输入方法
                    use_clipboard = self._should_use_clipboard(text)

                    if use_clipboard and PYPERCLIP_AVAILABLE:
                        # 方法1: 剪贴板输入（推荐用于中文和特殊字符）
                        success = self._input_via_clipboard(text, element_name)
                    else:
                        # 方法2: 直接输入（用于简单英文）
                        success = self._input_directly(text, element_name)

                    if success:
                        self.logger.info(f"✅ {element_name} 填写完成")
                        self.logger.info(f"   📝 已填写内容: '{text}'")
                        self.logger.info(f"   � 内容长度: {len(text)} 字符")
                        time.sleep(self.step_delay)
                        return True
                    else:
                        self.logger.warning(f"⚠️ 第 {attempt + 1} 次填写失败，准备重试")
                        continue
                else:
                    self.logger.warning(f"⚠️ {element_name} 填写内容为空")
                    return True

            except Exception as e:
                self.logger.error(f"❌ 第 {attempt + 1} 次填写 {element_name} 异常: {e}")
                if attempt == max_retries - 1:
                    self.logger.error(f"   📍 坐标: ({x}, {y})")
                    self.logger.error(f"   📝 内容: '{text}'")
                    traceback.print_exc()

        # 所有重试都失败
        self.logger.error(f"❌ {element_name} 填写失败，已达到最大重试次数 {max_retries}")
        return False

    def _click_submit_button(self) -> Union[bool, Dict[str, Any]]:
        """点击确定按钮提交申请（增强版：包含重试机制、验证和频率错误处理）"""
        if "确定按钮" not in self.coordinates:
            self.logger.error("❌ 未找到确定按钮坐标配置")
            return False

        x, y = self.coordinates["确定按钮"]
        self.logger.info(f"🖱️ 准备点击确定按钮")
        self.logger.info(f"   📍 坐标: ({x}, {y})")

        # 最多重试3次
        max_retries = 3
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    self.logger.info(f"🔄 第 {attempt + 1} 次尝试点击确定按钮")
                    time.sleep(1)  # 重试前等待

                # 点击确定按钮
                self.logger.info(f"🖱️ 点击确定按钮 (尝试 {attempt + 1}/{max_retries})")
                pyautogui.click(x, y)
                time.sleep(0.5)  # 点击后等待

                # 🆕 集成频率错误检测和处理
                frequency_error_result = self._handle_frequency_error_after_click()
                if frequency_error_result.get("handled_error", False):
                    self.logger.warning("⚠️ 检测到并处理了频率错误，已切换到微信2")
                    return {
                        "success": False,
                        "error_type": "frequency_error_handled",
                        "message": "操作过于频繁，已自动切换到微信2",
                        "switched_to_wechat2": True
                    }

                # 验证点击是否成功（通过检查窗口是否仍然存在）
                # 如果申请成功，好友申请窗口应该会关闭或变化
                time.sleep(1)  # 等待窗口响应

                # 检查窗口状态
                window = self._detect_friend_request_window()
                if not window:
                    # 窗口已关闭，说明申请可能已提交
                    self.logger.info("✅ 确定按钮点击成功，好友申请窗口已关闭")
                    return True
                else:
                    # 窗口仍然存在，可能需要重试
                    self.logger.warning(f"⚠️ 第 {attempt + 1} 次点击后窗口仍然存在，可能需要重试")
                    if attempt < max_retries - 1:
                        continue
                    else:
                        # 最后一次尝试，即使窗口存在也认为成功
                        self.logger.info("✅ 确定按钮点击完成（最后一次尝试）")
                        return True

            except Exception as e:
                self.logger.error(f"❌ 第 {attempt + 1} 次点击确定按钮异常: {e}")
                if attempt == max_retries - 1:
                    self.logger.error(f"   📍 坐标: ({x}, {y})")
                    traceback.print_exc()

        # 所有重试都失败
        self.logger.error(f"❌ 确定按钮点击失败，已达到最大重试次数 {max_retries}")
        return False

    def _handle_frequency_error_after_click(self) -> dict:
        """在点击确定按钮后处理频率错误（修复版：立即点击确定按钮）

        Returns:
            dict: 处理结果
        """
        try:
            # 导入频率错误处理器
            from .frequency_error_handler import FrequencyErrorHandler

            # 创建错误处理器
            error_handler = FrequencyErrorHandler(self.logger)

            # 检测频率错误
            self.logger.info("🔍 检测是否出现'操作过于频繁'错误...")
            detection_result = error_handler.detect_frequency_error_after_click(timeout=5.0)

            if detection_result.has_error:
                self.logger.warning(f"⚠️ 检测到频率错误: {detection_result.error_type}")
                self.logger.warning(f"📝 错误信息: {detection_result.error_message}")

                # 🆕 立即点击错误对话框的确定按钮
                self.logger.info("🚀 立即处理频率错误对话框...")
                click_success = error_handler._click_error_dialog_ok_button(detection_result)

                if click_success:
                    self.logger.info("✅ 频率错误对话框确定按钮点击成功")
                else:
                    self.logger.warning("⚠️ 频率错误对话框确定按钮点击失败，但继续处理")

                # 🆕 然后执行完整的频率错误处理流程
                self.logger.info("🔄 执行完整的频率错误处理流程...")
                handle_success = error_handler.handle_frequency_error(detection_result)

                if handle_success:
                    self.logger.info("✅ 频率错误处理完成，已切换到微信2")

                    # 🆕 确保设置重新开始标志
                    try:
                        error_handler._set_restart_flag()
                        self.logger.info("🔄 已设置重新开始标志")
                    except Exception as e:
                        self.logger.warning(f"⚠️ 设置重新开始标志失败: {e}")

                    return {
                        "handled_error": True,
                        "error_type": detection_result.error_type,
                        "switched_to_wechat2": True,
                        "success": True,
                        "immediate_click": True
                    }
                else:
                    # 如果处理失败，仍然返回已处理状态（因为至少点击了确定按钮）
                    self.logger.warning("⚠️ 频率错误完整处理失败，但已点击确定按钮")

                    return {
                        "handled_error": True,
                        "error_type": detection_result.error_type,
                        "switched_to_wechat2": False,
                        "success": False,
                        "immediate_click": True
                    }
            else:
                self.logger.info("✅ 未检测到频率错误，继续正常流程")
                return {
                    "handled_error": False,
                    "error_type": "none",
                    "switched_to_wechat2": False,
                    "success": True,
                    "immediate_click": False
                }

        except Exception as e:
            self.logger.error(f"❌ 频率错误处理异常: {e}")
            return {
                "handled_error": False,
                "error_type": "detection_error",
                "switched_to_wechat2": False,
                "success": False,
                "error_message": str(e),
                "immediate_click": False
            }

    def _call_demo_auto_handling(self) -> bool:
        """调用demo_auto_handling.py模块进行完整的自动化处理

        Returns:
            bool: 是否处理成功
        """
        try:
            self.logger.info("🚀 正在调用demo_auto_handling.py模块...")

            # 导入demo_auto_handling模块
            try:
                import sys
                import os

                # 确保可以导入demo_auto_handling模块
                current_dir = os.path.dirname(os.path.abspath(__file__))
                parent_dir = os.path.dirname(current_dir)
                demo_module_path = os.path.join(parent_dir, 'modules', 'demo_auto_handling.py')

                if not os.path.exists(demo_module_path):
                    # 尝试在当前目录查找
                    demo_module_path = os.path.join(current_dir, 'demo_auto_handling.py')
                    if not os.path.exists(demo_module_path):
                        self.logger.error("❌ 未找到demo_auto_handling.py模块")
                        return False

                # 动态导入模块
                spec = importlib.util.spec_from_file_location("demo_auto_handling", demo_module_path)
                if spec and spec.loader:
                    demo_module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(demo_module)
                else:
                    raise ImportError("无法创建模块规范")

                self.logger.info("✅ 成功导入demo_auto_handling模块")

            except Exception as import_error:
                self.logger.error(f"❌ 导入demo_auto_handling模块失败: {import_error}")
                self.logger.warning("⚠️ demo_auto_handling模块不存在，将使用基础处理方式")
                demo_module = None

            # 调用demo_automatic_handling函数
            self.logger.info("🔄 开始执行demo_automatic_handling()...")

            # 记录调用开始时间
            start_time = time.time()

            # 执行自动处理
            if demo_module and hasattr(demo_module, 'demo_automatic_handling'):
                demo_module.demo_automatic_handling()

                # 计算执行时间
                execution_time = time.time() - start_time

                self.logger.info(f"✅ demo_auto_handling.py 执行完成")
                self.logger.info(f"⏱️ 执行耗时: {execution_time:.2f}秒")
                self.logger.info("🚀 系统应已切换到微信2，可以继续执行后续操作")
            else:
                self.logger.info("ℹ️ demo_module不可用，使用基础处理方式")

            return True

        except Exception as e:
            self.logger.error(f"❌ 调用demo_auto_handling.py失败: {e}")
            import traceback
            self.logger.error(f"📋 详细错误信息:\n{traceback.format_exc()}")
            return False

    def _should_use_clipboard(self, text: str) -> bool:
        """智能判断是否应该使用剪贴板输入"""
        # 包含中文字符
        if any('\u4e00' <= char <= '\u9fff' for char in text):
            return True

        # 包含特殊字符
        special_chars = ['@', '#', '$', '%', '^', '&', '*', '(', ')', '+', '=', '[', ']', '{', '}', '|', '\\', ':', ';', '"', "'", '<', '>', ',', '.', '?', '/', '~', '`']
        if any(char in text for char in special_chars):
            return True

        # 文本较长（超过20个字符）
        if len(text) > 20:
            return True

        # 包含空格
        if ' ' in text:
            return True

        return False

    def _input_via_clipboard(self, text: str, element_name: str) -> bool:
        """使用剪贴板方法输入文本（增强版）"""
        try:
            self.logger.info(f"📋 使用剪贴板方法输入文本到{element_name}")
            self.logger.info(f"   📝 原始文本: '{text}'")
            self.logger.info(f"   📏 文本长度: {len(text)} 字符")

            # 🔧 增强：备份当前剪贴板内容
            try:
                original_clipboard = pyperclip.paste()
                self.logger.info(f"   💾 备份剪贴板内容: '{original_clipboard[:50]}...' (前50字符)")
            except:
                original_clipboard = ""

            # 🔧 增强：多次尝试复制到剪贴板
            max_copy_attempts = 3
            for copy_attempt in range(max_copy_attempts):
                try:
                    pyperclip.copy(text)
                    time.sleep(0.3)

                    # 验证剪贴板内容
                    clipboard_content = pyperclip.paste()
                    if clipboard_content == text:
                        self.logger.info(f"✅ 第{copy_attempt+1}次复制成功")
                        break
                    else:
                        self.logger.warning(f"⚠️ 第{copy_attempt+1}次复制失败: 期望'{text}', 实际'{clipboard_content}'")
                        if copy_attempt == max_copy_attempts - 1:
                            self.logger.error("❌ 多次复制失败，无法继续")
                            return False
                except Exception as e:
                    self.logger.error(f"❌ 第{copy_attempt+1}次复制异常: {e}")
                    if copy_attempt == max_copy_attempts - 1:
                        return False

            # 🔧 增强：多次尝试粘贴
            max_paste_attempts = 2
            for paste_attempt in range(max_paste_attempts):
                try:
                    self.logger.info(f"📋 第{paste_attempt+1}次粘贴尝试")
                    pyautogui.hotkey('ctrl', 'v')
                    time.sleep(0.8)

                    # 验证粘贴是否成功（这里我们假设成功，实际验证需要OCR）
                    self.logger.info(f"✅ 第{paste_attempt+1}次粘贴完成")
                    break
                except Exception as e:
                    self.logger.error(f"❌ 第{paste_attempt+1}次粘贴异常: {e}")
                    if paste_attempt == max_paste_attempts - 1:
                        return False

            # 🔧 增强：恢复原始剪贴板内容
            try:
                if original_clipboard:
                    pyperclip.copy(original_clipboard)
                    self.logger.info("✅ 剪贴板内容已恢复")
            except:
                pass

            self.logger.info(f"✅ 剪贴板输入完成: '{text}'")
            return True

        except Exception as e:
            self.logger.error(f"❌ 剪贴板输入失败: {e}")
            return False

    def _input_directly(self, text: str, element_name: str) -> bool:
        """直接输入文本"""
        try:
            self.logger.info(f"⌨️ 使用直接输入方法到{element_name}")
            self.logger.info(f"   � 输入文本: '{text}'")

            # 使用更慢的输入速度确保稳定性
            pyautogui.write(text, interval=0.15)
            time.sleep(0.8)

            self.logger.info(f"✅ 直接输入完成")
            return True

        except Exception as e:
            self.logger.error(f"❌ 直接输入失败: {e}")
            return False

    def execute_friend_request_flow(self, phone: str, verification_msg: str):
        """执行好友申请流程

        Args:
            phone: 手机号
            verification_msg: 验证信息（已弃用，将从Excel中读取）

        Returns:
            Dict: 包含status和message的结果字典
        """
        try:
            self.logger.info(f"🚀 开始执行好友申请流程: {phone}")

            # 🔧 调试：记录调用信息
            import traceback
            stack = traceback.extract_stack()
            self.logger.info("🔍 execute_friend_request_flow调用栈:")
            for i, frame in enumerate(stack[-3:-1]):  # 显示最近的2个调用
                self.logger.info(f"   {i+1}. {frame.filename}:{frame.lineno} in {frame.name}")

            self.logger.info(f"🔍 execute_friend_request_flow接收到的参数:")
            self.logger.info(f"   📱 phone: '{phone}'")
            self.logger.info(f"   📝 verification_msg: '{verification_msg}' (应该被忽略)")

            # 🔧 修复：从Excel中读取验证信息，而不是使用传入的硬编码验证信息
            self.logger.info("📋 从Excel中读取联系人信息...")

            # 加载Excel数据
            df = self.load_excel_data()
            if df is None:
                self.logger.error("❌ 无法加载Excel数据")
                return {
                    "status": "error",
                    "message": "无法加载Excel数据",
                    "phone_number": phone
                }

            # 查找手机号对应的行
            phone_column = "手机号码"

            # 🔧 修复：处理数据类型匹配问题
            # Excel中的手机号可能是int64类型，需要进行类型转换匹配
            try:
                # 尝试数字匹配（Excel中手机号为数字类型）
                phone_num = int(phone)
                matched_rows = df[df[phone_column] == phone_num]
                self.logger.info(f"🔍 使用数字匹配查找手机号: {phone_num}")
            except (ValueError, TypeError):
                # 如果转换失败，使用字符串匹配
                matched_rows = df[df[phone_column].astype(str) == str(phone)]
                self.logger.info(f"🔍 使用字符串匹配查找手机号: {phone}")

            self.logger.info(f"📊 查找结果: 找到 {len(matched_rows)} 条匹配记录")

            if len(matched_rows) == 0:
                self.logger.warning(f"⚠️ 在Excel中未找到手机号 {phone} 对应的记录，使用默认信息")
                # 🔧 修复：Excel中找不到记录时，使用默认信息继续处理，而不是直接返回错误
                from datetime import datetime
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                excel_verification = "您好，我想加您为好友"  # 使用默认验证信息
                excel_remark = f"{phone}-未知-{current_time}"  # 使用默认备注格式
                self.logger.info(f"📝 使用默认验证信息: '{excel_verification}'")
                self.logger.info(f"📝 使用默认备注信息: '{excel_remark}'")
            else:
                # 获取第一个匹配的行并提取联系人信息
                row = matched_rows.iloc[0]
                contact_info = self.extract_contact_info(row)

                if not contact_info:
                    self.logger.warning(f"⚠️ 无法提取手机号 {phone} 的联系人信息，使用默认信息")
                    # 🔧 修复：提取失败时也使用默认信息继续处理
                    from datetime import datetime
                    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    excel_verification = "您好，我想加您为好友"
                    excel_remark = f"{phone}-未知-{current_time}"
                    self.logger.info(f"📝 使用默认验证信息: '{excel_verification}'")
                    self.logger.info(f"📝 使用默认备注信息: '{excel_remark}'")
                else:
                    # 使用Excel中的验证信息
                    excel_verification = contact_info.get("verification", "")
                    excel_remark = contact_info.get("remark", "")

            self.logger.info(f"📝 使用Excel中的验证信息: '{excel_verification}'")
            self.logger.info(f"📝 使用Excel中的备注信息: '{excel_remark}'")

            # 检测好友申请窗口
            window_hwnd = self._detect_friend_request_window()
            if not window_hwnd:
                self.logger.warning("⚠️ 未找到好友申请窗口")
                return {
                    "status": "error",
                    "message": "未找到好友申请窗口",
                    "phone_number": phone
                }

            # 激活窗口
            if not self._activate_window(window_hwnd):
                self.logger.error("❌ 激活好友申请窗口失败")
                return {
                    "status": "error",
                    "message": "激活窗口失败",
                    "phone_number": phone
                }

            # 填写验证信息和备注（使用Excel中的信息）
            if self._fill_and_submit_information(excel_verification, excel_remark):
                self.logger.info("✅ 好友申请流程执行成功")
                return {
                    "status": "success",
                    "message": "好友申请已发送",
                    "phone_number": phone
                }
            else:
                self.logger.error("❌ 填写信息失败")
                return {
                    "status": "error",
                    "message": "填写信息失败",
                    "phone_number": phone
                }

        except Exception as e:
            self.logger.error(f"❌ 执行好友申请流程失败: {e}")
            return {
                "status": "error",
                "message": f"执行失败: {e}",
                "phone_number": phone
            }

    def _generate_remark_from_phone(self, phone: str) -> Optional[str]:
        """根据手机号从Excel数据中查找对应的准考证号和姓名信息，生成备注

        Args:
            phone: 手机号

        Returns:
            Optional[str]: 生成的备注信息，格式为"准考证号-姓名-北京时间"，如果未找到则返回None
        """
        try:
            # 加载Excel数据
            df = self.load_excel_data()
            if df is None or len(df) == 0:
                self.logger.error("❌ 无法加载Excel数据")
                return None

            # 查找手机号对应的行
            # 先尝试"手机号码"列
            phone_column = "手机号码"
            if phone_column not in df.columns:
                # 如果没有"手机号码"列，尝试"手机号"列
                phone_column = "手机号"
                if phone_column not in df.columns:
                    self.logger.error("❌ Excel中没有手机号码或手机号列")
                    return None

            # 查找匹配的行
            # 🔧 修复：处理数据类型匹配问题
            try:
                # 尝试数字匹配（Excel中手机号为数字类型）
                phone_num = int(phone)
                matched_rows = df[df[phone_column] == phone_num]
                self.logger.info(f"🔍 使用数字匹配查找手机号: {phone_num}")
            except (ValueError, TypeError):
                # 如果转换失败，使用字符串匹配
                matched_rows = df[df[phone_column].astype(str) == str(phone)]
                self.logger.info(f"🔍 使用字符串匹配查找手机号: {phone}")

            self.logger.info(f"📊 查找结果: 找到 {len(matched_rows)} 条匹配记录")

            if len(matched_rows) == 0:
                self.logger.warning(f"⚠️ 在Excel中未找到手机号 {phone} 对应的记录")
                return None

            # 获取第一个匹配的行
            row = matched_rows.iloc[0]

            # 提取准考证号和姓名
            exam_id = None
            if "准考证" in row.index and pd.notna(row["准考证"]) and str(row["准考证"]).strip():
                exam_id = str(row["准考证"]).strip()

            name = None
            if "姓名" in row.index and pd.notna(row["姓名"]) and str(row["姓名"]).strip():
                name = str(row["姓名"]).strip()

            if not exam_id or not name:
                self.logger.warning(f"⚠️ 手机号 {phone} 对应的准考证号或姓名为空")
                return None

            # 生成备注信息（格式：准考证号-姓名-北京时间）
            # 修复：使用完整的北京时间格式，与其他地方保持一致，使用正确的北京时区
            beijing_tz = timezone(timedelta(hours=8))
            current_time = datetime.now(beijing_tz).strftime("%Y-%m-%d %H:%M:%S")
            remark = f"{exam_id}-{name}-{current_time}"

            self.logger.info(f"✅ 成功生成备注信息: {remark}")
            return remark

        except Exception as e:
            self.logger.error(f"❌ 生成备注信息失败: {e}")
            return None

    def _validate_contact_data(self, contact_info: Dict) -> bool:
        """验证联系人数据的完整性和格式正确性

        Args:
            contact_info: 联系人信息字典

        Returns:
            True如果数据验证通过，False如果验证失败
        """
        try:
            # 验证必要字段
            required_fields = ["phone", "verification", "remark", "exam_id", "name"]
            for field in required_fields:
                if field not in contact_info or not contact_info[field]:
                    self.logger.error(f"❌ 数据验证失败：缺少必要字段 '{field}'")
                    return False

            # 验证手机号格式（简单验证）
            phone = contact_info["phone"]
            if not phone.isdigit() or len(phone) != 11:
                self.logger.error(f"❌ 数据验证失败：手机号格式不正确 '{phone}'")
                return False

            # 验证准考证号格式
            exam_id = contact_info["exam_id"]
            if len(exam_id) < 6:  # 准考证号至少6位
                self.logger.error(f"❌ 数据验证失败：准考证号格式不正确 '{exam_id}'")
                return False

            # 验证姓名
            name = contact_info["name"]
            if len(name) < 2 or len(name) > 10:  # 姓名长度合理范围
                self.logger.error(f"❌ 数据验证失败：姓名长度不合理 '{name}'")
                return False

            # 验证备注信息格式（准考证号-姓名-北京时间）
            remark = contact_info["remark"]
            remark_parts = remark.split('-')
            if len(remark_parts) < 3:
                self.logger.error(f"❌ 数据验证失败：备注信息格式不正确 '{remark}'，期望格式：准考证号-姓名-北京时间")
                return False

            # 验证备注信息中的准考证号和姓名是否匹配
            remark_exam_id = remark_parts[0]
            remark_name = remark_parts[1]
            if remark_exam_id != exam_id:
                self.logger.error(f"❌ 数据验证失败：备注中的准考证号 '{remark_exam_id}' 与实际准考证号 '{exam_id}' 不匹配")
                return False

            if remark_name != name:
                self.logger.error(f"❌ 数据验证失败：备注中的姓名 '{remark_name}' 与实际姓名 '{name}' 不匹配")
                return False

            # 验证时间格式
            remark_time = '-'.join(remark_parts[2:])  # 时间部分可能包含多个'-'
            try:
                datetime.strptime(remark_time, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                self.logger.error(f"❌ 数据验证失败：备注中的时间格式不正确 '{remark_time}'，期望格式：YYYY-MM-DD HH:MM:SS")
                return False

            self.logger.info("✅ 联系人数据验证通过")
            return True

        except Exception as e:
            self.logger.error(f"❌ 数据验证过程失败: {e}")
            return False

    def generate_report(self) -> Dict:
        """生成处理报告"""
        duration = (self.stats["end_time"] or datetime.now()) - self.stats["start_time"]
        duration_seconds = duration.total_seconds()

        report = {
            "总联系人数": self.stats["total"],
            "成功处理数": self.stats["success"],
            "失败数": self.stats["failed"],
            "跳过数": self.stats["skipped"],
            "开始时间": self.stats["start_time"].strftime("%Y-%m-%d %H:%M:%S"),
            "结束时间": (self.stats["end_time"] or datetime.now()).strftime("%Y-%m-%d %H:%M:%S"),
            "总耗时": f"{int(duration_seconds // 3600)}小时{int((duration_seconds % 3600) // 60)}分{int(duration_seconds % 60)}秒",
            "成功率": f"{self.stats['success'] / self.stats['total'] * 100:.1f}%" if self.stats["total"] > 0 else "0%"
        }

        # 记录报告
        self.logger.info("📊 处理报告:")
        for key, value in report.items():
            self.logger.info(f"  {key}: {value}")

        return report


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="微信好友申请窗口自动化处理工具")
    parser.add_argument("-c", "--config", help="配置文件路径")
    parser.add_argument("-d", "--debug", action="store_true", help="启用调试模式")
    args = parser.parse_args()

    # 创建处理器
    processor = FriendRequestProcessor(args.config)

    # 如果命令行指定了调试模式，覆盖配置
    if args.debug:
        processor.config["debug_mode"] = True

    # 处理所有联系人
    processor.process_all_contacts()


if __name__ == "__main__":
    main()