2025-07-31 03:44:19,474 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-31 03:44:19,494 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-31 03:44:19,506 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 有效微信窗口统计: 0 个
2025-07-31 03:44:19,520 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 0 个微信窗口
2025-07-31 03:44:19,526 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-31 03:44:19,535 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-31 03:44:19,537 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-31 03:44:19,542 - WeChatAutoAdd - INFO - 创建截图目录: screenshots
2025-07-31 03:44:19,555 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-31 03:44:19,557 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-31 03:44:19,557 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-31 03:44:19,559 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-31 03:44:19,561 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-31 03:44:19,584 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-31 03:44:19,592 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-31 03:44:19,611 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-31 03:44:19,625 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250731_034419.log
2025-07-31 03:44:19,628 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-31 03:44:19,629 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-31 03:44:19,690 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-31 03:44:19,719 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-31 03:44:19,761 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-31 03:44:19,777 - main_controller - INFO - 🧠 启用智能检测功能...
2025-07-31 03:44:19,807 - main_controller - INFO - ✅ 智能检测功能已启用
2025-07-31 03:44:19,826 - main_controller - INFO - 🔍 支持的检测方法:
2025-07-31 03:44:19,834 - main_controller - INFO -    1. 通过子控件检测确定按钮
2025-07-31 03:44:19,840 - main_controller - INFO -    2. 通过文本识别检测确定按钮
2025-07-31 03:44:19,871 - main_controller - INFO -    3. 通过图像识别检测确定按钮
2025-07-31 03:44:19,874 - main_controller - INFO -    4. 基于相对位置计算确定按钮坐标
2025-07-31 03:44:19,884 - main_controller - INFO - 🖱️ 支持的点击方法:
2025-07-31 03:44:19,886 - main_controller - INFO -    1. 使用Win32 API点击
2025-07-31 03:44:19,887 - main_controller - INFO -    2. 使用PyAutoGUI点击
2025-07-31 03:44:19,888 - main_controller - INFO -    3. 使用键盘Enter键
2025-07-31 03:44:19,888 - main_controller - INFO -    4. 发送窗口消息
2025-07-31 03:44:19,889 - main_controller - INFO - 🔄 重试设置: 检测最多3次, 点击最多4次
2025-07-31 03:44:19,891 - main_controller - INFO - 📱 已启用跨分辨率兼容性
2025-07-31 03:44:19,896 - main_controller - INFO - 🪟 已启用窗口位置无关性
2025-07-31 03:44:19,905 - main_controller - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-31 03:44:19,907 - main_controller - INFO - 📅 当前北京时间: 2025-07-31 11:44:19
2025-07-31 03:44:19,911 - main_controller - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-31 03:44:19,970 - main_controller - INFO - 📅 启动时间: 2025-07-31 11:44:19
2025-07-31 03:44:19,989 - main_controller - INFO - 🔍 开始扫描微信窗口...
2025-07-31 03:44:20,007 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-31 03:44:20,035 - modules.window_manager - INFO - 🎯 总共找到 0 个微信窗口
2025-07-31 03:44:20,045 - main_controller - ERROR - ❌ 未找到任何微信窗口
2025-07-31 03:44:20,062 - main_controller - ERROR - ❌ 未找到微信窗口，程序退出
