2025-08-01 00:55:16,084 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 00:55:16,085 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 00:55:16,085 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 00:55:16,088 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-08-01 00:55:16,089 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-08-01 00:55:16,092 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 有效微信窗口统计: 0 个
2025-08-01 00:55:16,092 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 0 个微信窗口
2025-08-01 00:55:16,094 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 00:55:16,095 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 00:55:16,097 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-08-01 00:55:16,099 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,103 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-08-01 00:55:16,103 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-08-01 00:55:16,105 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-08-01 00:55:16,105 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-08-01 00:55:16,119 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-08-01 00:55:16,127 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-08-01 00:55:16,132 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-08-01 00:55:16,134 - main_controller - INFO - 🧠 启用智能检测功能...
2025-08-01 00:55:16,134 - main_controller - INFO - ✅ 智能检测功能已启用
2025-08-01 00:55:16,135 - main_controller - INFO - 🔍 支持的检测方法:
2025-08-01 00:55:16,135 - main_controller - INFO -    1. 通过子控件检测确定按钮
2025-08-01 00:55:16,136 - main_controller - INFO -    2. 通过文本识别检测确定按钮
2025-08-01 00:55:16,137 - main_controller - INFO -    3. 通过图像识别检测确定按钮
2025-08-01 00:55:16,137 - main_controller - INFO -    4. 基于相对位置计算确定按钮坐标
2025-08-01 00:55:16,138 - main_controller - INFO - 🖱️ 支持的点击方法:
2025-08-01 00:55:16,139 - main_controller - INFO -    1. 使用Win32 API点击
2025-08-01 00:55:16,140 - main_controller - INFO -    2. 使用PyAutoGUI点击
2025-08-01 00:55:16,148 - main_controller - INFO -    3. 使用键盘Enter键
2025-08-01 00:55:16,150 - main_controller - INFO -    4. 发送窗口消息
2025-08-01 00:55:16,159 - main_controller - INFO - 🔄 重试设置: 检测最多3次, 点击最多4次
2025-08-01 00:55:16,159 - main_controller - INFO - 📱 已启用跨分辨率兼容性
2025-08-01 00:55:16,160 - main_controller - INFO - 🪟 已启用窗口位置无关性
2025-08-01 00:55:16,160 - main_controller - INFO - ✅ 微信自动化主控制器初始化完成
2025-08-01 00:55:16,161 - main_controller - INFO - 📅 当前北京时间: 2025-08-01 08:55:16
2025-08-01 00:55:16,167 - main_controller - ERROR - ❌ 检查时间段配置失败: 'WeChatMainController' object has no attribute 'config'
2025-08-01 00:55:16,174 - main_controller - ERROR - ❌ 检查时间段失败: 'WeChatMainController' object has no attribute 'config'
2025-08-01 00:55:16,218 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 00:55:16,220 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 00:55:16,225 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 00:55:16,227 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-08-01 00:55:16,231 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-08-01 00:55:16,233 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 有效微信窗口统计: 0 个
2025-08-01 00:55:16,234 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 0 个微信窗口
2025-08-01 00:55:16,250 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 00:55:16,278 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 00:55:16,281 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-08-01 00:55:16,291 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,296 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-08-01 00:55:16,299 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-08-01 00:55:16,303 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-08-01 00:55:16,304 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-08-01 00:55:16,305 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-08-01 00:55:16,305 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-08-01 00:55:16,306 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-08-01 00:55:16,306 - main_controller - INFO - 🧠 启用智能检测功能...
2025-08-01 00:55:16,306 - main_controller - INFO - ✅ 智能检测功能已启用
2025-08-01 00:55:16,307 - main_controller - INFO - 🔍 支持的检测方法:
2025-08-01 00:55:16,307 - main_controller - INFO -    1. 通过子控件检测确定按钮
2025-08-01 00:55:16,307 - main_controller - INFO -    2. 通过文本识别检测确定按钮
2025-08-01 00:55:16,309 - main_controller - INFO -    3. 通过图像识别检测确定按钮
2025-08-01 00:55:16,310 - main_controller - INFO -    4. 基于相对位置计算确定按钮坐标
2025-08-01 00:55:16,311 - main_controller - INFO - 🖱️ 支持的点击方法:
2025-08-01 00:55:16,312 - main_controller - INFO -    1. 使用Win32 API点击
2025-08-01 00:55:16,313 - main_controller - INFO -    2. 使用PyAutoGUI点击
2025-08-01 00:55:16,314 - main_controller - INFO -    3. 使用键盘Enter键
2025-08-01 00:55:16,315 - main_controller - INFO -    4. 发送窗口消息
2025-08-01 00:55:16,324 - main_controller - INFO - 🔄 重试设置: 检测最多3次, 点击最多4次
2025-08-01 00:55:16,325 - main_controller - INFO - 📱 已启用跨分辨率兼容性
2025-08-01 00:55:16,326 - main_controller - INFO - 🪟 已启用窗口位置无关性
2025-08-01 00:55:16,326 - main_controller - INFO - ✅ 微信自动化主控制器初始化完成
2025-08-01 00:55:16,326 - main_controller - INFO - 📅 当前北京时间: 2024-01-01 08:00:00
2025-08-01 00:55:16,327 - main_controller - ERROR - ❌ 检查时间段配置失败: 'WeChatMainController' object has no attribute 'config'
2025-08-01 00:55:16,329 - main_controller - ERROR - ❌ 检查时间段失败: 'WeChatMainController' object has no attribute 'config'
2025-08-01 00:55:16,340 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 00:55:16,344 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 00:55:16,351 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 00:55:16,354 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-08-01 00:55:16,355 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-08-01 00:55:16,358 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 有效微信窗口统计: 0 个
2025-08-01 00:55:16,359 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 0 个微信窗口
2025-08-01 00:55:16,368 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 00:55:16,370 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 00:55:16,372 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-08-01 00:55:16,373 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,384 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-08-01 00:55:16,387 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-08-01 00:55:16,392 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-08-01 00:55:16,393 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-08-01 00:55:16,401 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-08-01 00:55:16,401 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-08-01 00:55:16,402 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-08-01 00:55:16,402 - main_controller - INFO - 🧠 启用智能检测功能...
2025-08-01 00:55:16,403 - main_controller - INFO - ✅ 智能检测功能已启用
2025-08-01 00:55:16,403 - main_controller - INFO - 🔍 支持的检测方法:
2025-08-01 00:55:16,404 - main_controller - INFO -    1. 通过子控件检测确定按钮
2025-08-01 00:55:16,405 - main_controller - INFO -    2. 通过文本识别检测确定按钮
2025-08-01 00:55:16,408 - main_controller - INFO -    3. 通过图像识别检测确定按钮
2025-08-01 00:55:16,409 - main_controller - INFO -    4. 基于相对位置计算确定按钮坐标
2025-08-01 00:55:16,420 - main_controller - INFO - 🖱️ 支持的点击方法:
2025-08-01 00:55:16,427 - main_controller - INFO -    1. 使用Win32 API点击
2025-08-01 00:55:16,433 - main_controller - INFO -    2. 使用PyAutoGUI点击
2025-08-01 00:55:16,435 - main_controller - INFO -    3. 使用键盘Enter键
2025-08-01 00:55:16,436 - main_controller - INFO -    4. 发送窗口消息
2025-08-01 00:55:16,437 - main_controller - INFO - 🔄 重试设置: 检测最多3次, 点击最多4次
2025-08-01 00:55:16,438 - main_controller - INFO - 📱 已启用跨分辨率兼容性
2025-08-01 00:55:16,439 - main_controller - INFO - 🪟 已启用窗口位置无关性
2025-08-01 00:55:16,440 - main_controller - INFO - ✅ 微信自动化主控制器初始化完成
2025-08-01 00:55:16,441 - main_controller - INFO - 📅 当前北京时间: 2024-01-01 10:30:00
2025-08-01 00:55:16,454 - main_controller - ERROR - ❌ 检查时间段配置失败: 'WeChatMainController' object has no attribute 'config'
2025-08-01 00:55:16,456 - main_controller - ERROR - ❌ 检查时间段失败: 'WeChatMainController' object has no attribute 'config'
2025-08-01 00:55:16,469 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 00:55:16,472 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 00:55:16,488 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 00:55:16,505 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-08-01 00:55:16,506 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-08-01 00:55:16,515 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 有效微信窗口统计: 0 个
2025-08-01 00:55:16,525 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 0 个微信窗口
2025-08-01 00:55:16,533 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 00:55:16,540 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 00:55:16,544 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-08-01 00:55:16,550 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,555 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-08-01 00:55:16,559 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-08-01 00:55:16,559 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-08-01 00:55:16,560 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-08-01 00:55:16,561 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-08-01 00:55:16,563 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-08-01 00:55:16,563 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-08-01 00:55:16,567 - main_controller - INFO - 🧠 启用智能检测功能...
2025-08-01 00:55:16,568 - main_controller - INFO - ✅ 智能检测功能已启用
2025-08-01 00:55:16,569 - main_controller - INFO - 🔍 支持的检测方法:
2025-08-01 00:55:16,569 - main_controller - INFO -    1. 通过子控件检测确定按钮
2025-08-01 00:55:16,570 - main_controller - INFO -    2. 通过文本识别检测确定按钮
2025-08-01 00:55:16,574 - main_controller - INFO -    3. 通过图像识别检测确定按钮
2025-08-01 00:55:16,576 - main_controller - INFO -    4. 基于相对位置计算确定按钮坐标
2025-08-01 00:55:16,577 - main_controller - INFO - 🖱️ 支持的点击方法:
2025-08-01 00:55:16,586 - main_controller - INFO -    1. 使用Win32 API点击
2025-08-01 00:55:16,587 - main_controller - INFO -    2. 使用PyAutoGUI点击
2025-08-01 00:55:16,588 - main_controller - INFO -    3. 使用键盘Enter键
2025-08-01 00:55:16,592 - main_controller - INFO -    4. 发送窗口消息
2025-08-01 00:55:16,592 - main_controller - INFO - 🔄 重试设置: 检测最多3次, 点击最多4次
2025-08-01 00:55:16,593 - main_controller - INFO - 📱 已启用跨分辨率兼容性
2025-08-01 00:55:16,593 - main_controller - INFO - 🪟 已启用窗口位置无关性
2025-08-01 00:55:16,594 - main_controller - INFO - ✅ 微信自动化主控制器初始化完成
2025-08-01 00:55:16,602 - main_controller - INFO - 📅 当前北京时间: 2024-01-01 13:00:00
2025-08-01 00:55:16,604 - main_controller - ERROR - ❌ 检查时间段配置失败: 'WeChatMainController' object has no attribute 'config'
2025-08-01 00:55:16,605 - main_controller - ERROR - ❌ 检查时间段失败: 'WeChatMainController' object has no attribute 'config'
2025-08-01 00:55:16,621 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 00:55:16,624 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 00:55:16,625 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 00:55:16,633 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-08-01 00:55:16,638 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-08-01 00:55:16,640 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 有效微信窗口统计: 0 个
2025-08-01 00:55:16,661 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 0 个微信窗口
2025-08-01 00:55:16,671 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 00:55:16,672 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 00:55:16,673 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-08-01 00:55:16,674 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,687 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-08-01 00:55:16,688 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-08-01 00:55:16,688 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-08-01 00:55:16,689 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-08-01 00:55:16,692 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-08-01 00:55:16,693 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-08-01 00:55:16,693 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-08-01 00:55:16,694 - main_controller - INFO - 🧠 启用智能检测功能...
2025-08-01 00:55:16,694 - main_controller - INFO - ✅ 智能检测功能已启用
2025-08-01 00:55:16,694 - main_controller - INFO - 🔍 支持的检测方法:
2025-08-01 00:55:16,701 - main_controller - INFO -    1. 通过子控件检测确定按钮
2025-08-01 00:55:16,702 - main_controller - INFO -    2. 通过文本识别检测确定按钮
2025-08-01 00:55:16,702 - main_controller - INFO -    3. 通过图像识别检测确定按钮
2025-08-01 00:55:16,702 - main_controller - INFO -    4. 基于相对位置计算确定按钮坐标
2025-08-01 00:55:16,703 - main_controller - INFO - 🖱️ 支持的点击方法:
2025-08-01 00:55:16,703 - main_controller - INFO -    1. 使用Win32 API点击
2025-08-01 00:55:16,704 - main_controller - INFO -    2. 使用PyAutoGUI点击
2025-08-01 00:55:16,705 - main_controller - INFO -    3. 使用键盘Enter键
2025-08-01 00:55:16,705 - main_controller - INFO -    4. 发送窗口消息
2025-08-01 00:55:16,706 - main_controller - INFO - 🔄 重试设置: 检测最多3次, 点击最多4次
2025-08-01 00:55:16,707 - main_controller - INFO - 📱 已启用跨分辨率兼容性
2025-08-01 00:55:16,709 - main_controller - INFO - 🪟 已启用窗口位置无关性
2025-08-01 00:55:16,711 - main_controller - INFO - ✅ 微信自动化主控制器初始化完成
2025-08-01 00:55:16,721 - main_controller - INFO - 📅 当前北京时间: 2024-01-01 15:30:00
2025-08-01 00:55:16,722 - main_controller - ERROR - ❌ 检查时间段配置失败: 'WeChatMainController' object has no attribute 'config'
2025-08-01 00:55:16,727 - main_controller - ERROR - ❌ 检查时间段失败: 'WeChatMainController' object has no attribute 'config'
2025-08-01 00:55:16,740 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 00:55:16,741 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 00:55:16,742 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 00:55:16,744 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-08-01 00:55:16,754 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-08-01 00:55:16,756 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 有效微信窗口统计: 0 个
2025-08-01 00:55:16,760 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 0 个微信窗口
2025-08-01 00:55:16,768 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 00:55:16,770 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 00:55:16,770 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-08-01 00:55:16,771 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,774 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-08-01 00:55:16,774 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-08-01 00:55:16,784 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-08-01 00:55:16,791 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-08-01 00:55:16,792 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-08-01 00:55:16,792 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-08-01 00:55:16,792 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-08-01 00:55:16,793 - main_controller - INFO - 🧠 启用智能检测功能...
2025-08-01 00:55:16,793 - main_controller - INFO - ✅ 智能检测功能已启用
2025-08-01 00:55:16,794 - main_controller - INFO - 🔍 支持的检测方法:
2025-08-01 00:55:16,799 - main_controller - INFO -    1. 通过子控件检测确定按钮
2025-08-01 00:55:16,800 - main_controller - INFO -    2. 通过文本识别检测确定按钮
2025-08-01 00:55:16,801 - main_controller - INFO -    3. 通过图像识别检测确定按钮
2025-08-01 00:55:16,803 - main_controller - INFO -    4. 基于相对位置计算确定按钮坐标
2025-08-01 00:55:16,804 - main_controller - INFO - 🖱️ 支持的点击方法:
2025-08-01 00:55:16,805 - main_controller - INFO -    1. 使用Win32 API点击
2025-08-01 00:55:16,805 - main_controller - INFO -    2. 使用PyAutoGUI点击
2025-08-01 00:55:16,805 - main_controller - INFO -    3. 使用键盘Enter键
2025-08-01 00:55:16,806 - main_controller - INFO -    4. 发送窗口消息
2025-08-01 00:55:16,807 - main_controller - INFO - 🔄 重试设置: 检测最多3次, 点击最多4次
2025-08-01 00:55:16,807 - main_controller - INFO - 📱 已启用跨分辨率兼容性
2025-08-01 00:55:16,808 - main_controller - INFO - 🪟 已启用窗口位置无关性
2025-08-01 00:55:16,809 - main_controller - INFO - ✅ 微信自动化主控制器初始化完成
2025-08-01 00:55:16,810 - main_controller - INFO - 📅 当前北京时间: 2024-01-01 20:00:00
2025-08-01 00:55:16,825 - main_controller - ERROR - ❌ 检查时间段配置失败: 'WeChatMainController' object has no attribute 'config'
2025-08-01 00:55:16,826 - main_controller - ERROR - ❌ 检查时间段失败: 'WeChatMainController' object has no attribute 'config'
2025-08-01 00:55:16,860 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 00:55:16,871 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 00:55:16,873 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 00:55:16,878 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-08-01 00:55:16,885 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-08-01 00:55:16,891 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 有效微信窗口统计: 0 个
2025-08-01 00:55:16,892 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 0 个微信窗口
2025-08-01 00:55:16,899 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 00:55:16,902 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 00:55:16,902 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-08-01 00:55:16,903 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,906 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-08-01 00:55:16,907 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-08-01 00:55:16,907 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-08-01 00:55:16,909 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-08-01 00:55:16,911 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-08-01 00:55:16,917 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-08-01 00:55:16,921 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-08-01 00:55:16,922 - main_controller - INFO - 🧠 启用智能检测功能...
2025-08-01 00:55:16,925 - main_controller - INFO - ✅ 智能检测功能已启用
2025-08-01 00:55:16,927 - main_controller - INFO - 🔍 支持的检测方法:
2025-08-01 00:55:16,935 - main_controller - INFO -    1. 通过子控件检测确定按钮
2025-08-01 00:55:16,937 - main_controller - INFO -    2. 通过文本识别检测确定按钮
2025-08-01 00:55:16,942 - main_controller - INFO -    3. 通过图像识别检测确定按钮
2025-08-01 00:55:16,958 - main_controller - INFO -    4. 基于相对位置计算确定按钮坐标
2025-08-01 00:55:16,959 - main_controller - INFO - 🖱️ 支持的点击方法:
2025-08-01 00:55:16,960 - main_controller - INFO -    1. 使用Win32 API点击
2025-08-01 00:55:16,977 - main_controller - INFO -    2. 使用PyAutoGUI点击
2025-08-01 00:55:16,993 - main_controller - INFO -    3. 使用键盘Enter键
2025-08-01 00:55:17,006 - main_controller - INFO -    4. 发送窗口消息
2025-08-01 00:55:17,009 - main_controller - INFO - 🔄 重试设置: 检测最多3次, 点击最多4次
2025-08-01 00:55:17,021 - main_controller - INFO - 📱 已启用跨分辨率兼容性
2025-08-01 00:55:17,028 - main_controller - INFO - 🪟 已启用窗口位置无关性
2025-08-01 00:55:17,040 - main_controller - INFO - ✅ 微信自动化主控制器初始化完成
2025-08-01 00:55:17,059 - main_controller - INFO - 📅 当前北京时间: 2024-01-01 02:00:00
2025-08-01 00:55:17,072 - main_controller - ERROR - ❌ 检查时间段配置失败: 'WeChatMainController' object has no attribute 'config'
2025-08-01 00:55:17,091 - main_controller - ERROR - ❌ 检查时间段失败: 'WeChatMainController' object has no attribute 'config'
