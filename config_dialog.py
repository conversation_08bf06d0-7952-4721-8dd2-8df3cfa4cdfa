#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信自动化添加好友 - 配置对话框
提供友好的配置编辑界面

版本：1.0.0
作者：AI助手
创建时间：2025-01-28
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
from pathlib import Path
from typing import Dict, Any


class ConfigDialog:
    """配置对话框"""
    
    def __init__(self, parent, config_file: str = "config.json"):
        """初始化配置对话框"""
        self.parent = parent
        self.config_file = config_file
        self.config_data = {}
        self.result = None
        
        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("配置设置")
        self.dialog.geometry("800x600")
        self.dialog.resizable(True, True)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 加载配置
        self.load_config()
        
        # 创建界面
        self.create_widgets()
        
        # 绑定事件
        self.dialog.protocol("WM_DELETE_WINDOW", self.on_cancel)
    
    def center_window(self):
        """窗口居中"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (800 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (600 // 2)
        self.dialog.geometry(f"800x600+{x}+{y}")
    
    def load_config(self):
        """加载配置文件"""
        try:
            if Path(self.config_file).exists():
                with open(self.config_file, "r", encoding="utf-8") as f:
                    self.config_data = json.load(f)
            else:
                self.config_data = self.get_default_config()
        except Exception as e:
            messagebox.showerror("错误", f"加载配置文件失败: {e}")
            self.config_data = self.get_default_config()
    
    def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "excel_file": "添加好友名单.xlsx",
            "batch_size": 10,
            "delay_range": [1.5, 3.0],
            "click_delay": [0.3, 0.8],
            "retry_times": 3,
            "window_position": [0, 0],
            "multi_window": {
                "enabled": True,
                "contacts_per_window": 2,
                "switch_delay": 3,
                "max_windows": 5
            },
            "safety": {
                "max_operations_per_hour": 100,
                "max_runtime_hours": 8,
                "cool_down_time": 300
            }
        }
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建笔记本控件
        self.notebook = ttk.Notebook(self.dialog)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建各个配置页面
        self.create_basic_tab()
        self.create_timing_tab()
        self.create_window_tab()
        self.create_safety_tab()
        
        # 创建按钮框架
        self.create_buttons()
    
    def create_basic_tab(self):
        """创建基本设置标签页"""
        basic_frame = ttk.Frame(self.notebook)
        self.notebook.add(basic_frame, text="基本设置")
        
        # Excel文件设置
        excel_frame = ttk.LabelFrame(basic_frame, text="文件设置", padding=10)
        excel_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(excel_frame, text="Excel文件路径:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.excel_file_var = tk.StringVar(value=self.config_data.get("excel_file", ""))
        ttk.Entry(excel_frame, textvariable=self.excel_file_var, width=50).grid(row=0, column=1, padx=(10, 0), pady=2)
        
        # 批处理设置
        batch_frame = ttk.LabelFrame(basic_frame, text="批处理设置", padding=10)
        batch_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(batch_frame, text="批次大小:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.batch_size_var = tk.StringVar(value=str(self.config_data.get("batch_size", 10)))
        ttk.Entry(batch_frame, textvariable=self.batch_size_var, width=10).grid(row=0, column=1, padx=(10, 0), pady=2)
        
        ttk.Label(batch_frame, text="重试次数:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.retry_times_var = tk.StringVar(value=str(self.config_data.get("retry_times", 3)))
        ttk.Entry(batch_frame, textvariable=self.retry_times_var, width=10).grid(row=1, column=1, padx=(10, 0), pady=2)
    
    def create_timing_tab(self):
        """创建时间设置标签页"""
        timing_frame = ttk.Frame(self.notebook)
        self.notebook.add(timing_frame, text="时间设置")
        
        # 延迟设置
        delay_frame = ttk.LabelFrame(timing_frame, text="延迟设置", padding=10)
        delay_frame.pack(fill=tk.X, padx=5, pady=5)
        
        delay_range = self.config_data.get("delay_range", [1.5, 3.0])
        
        ttk.Label(delay_frame, text="操作延迟范围 (秒):").grid(row=0, column=0, sticky=tk.W, pady=2)
        
        delay_sub_frame = ttk.Frame(delay_frame)
        delay_sub_frame.grid(row=0, column=1, padx=(10, 0), pady=2)
        
        self.delay_min_var = tk.StringVar(value=str(delay_range[0]))
        ttk.Entry(delay_sub_frame, textvariable=self.delay_min_var, width=8).pack(side=tk.LEFT)
        ttk.Label(delay_sub_frame, text=" - ").pack(side=tk.LEFT)
        self.delay_max_var = tk.StringVar(value=str(delay_range[1]))
        ttk.Entry(delay_sub_frame, textvariable=self.delay_max_var, width=8).pack(side=tk.LEFT)
        
        # 点击延迟设置
        click_delay = self.config_data.get("click_delay", [0.3, 0.8])
        
        ttk.Label(delay_frame, text="点击延迟范围 (秒):").grid(row=1, column=0, sticky=tk.W, pady=2)
        
        click_sub_frame = ttk.Frame(delay_frame)
        click_sub_frame.grid(row=1, column=1, padx=(10, 0), pady=2)
        
        self.click_min_var = tk.StringVar(value=str(click_delay[0]))
        ttk.Entry(click_sub_frame, textvariable=self.click_min_var, width=8).pack(side=tk.LEFT)
        ttk.Label(click_sub_frame, text=" - ").pack(side=tk.LEFT)
        self.click_max_var = tk.StringVar(value=str(click_delay[1]))
        ttk.Entry(click_sub_frame, textvariable=self.click_max_var, width=8).pack(side=tk.LEFT)
    
    def create_window_tab(self):
        """创建窗口设置标签页"""
        window_frame = ttk.Frame(self.notebook)
        self.notebook.add(window_frame, text="窗口设置")
        
        # 窗口位置设置
        position_frame = ttk.LabelFrame(window_frame, text="窗口位置", padding=10)
        position_frame.pack(fill=tk.X, padx=5, pady=5)
        
        window_position = self.config_data.get("window_position", [0, 0])
        
        ttk.Label(position_frame, text="窗口位置 (X, Y):").grid(row=0, column=0, sticky=tk.W, pady=2)
        
        pos_sub_frame = ttk.Frame(position_frame)
        pos_sub_frame.grid(row=0, column=1, padx=(10, 0), pady=2)
        
        self.window_x_var = tk.StringVar(value=str(window_position[0]))
        ttk.Entry(pos_sub_frame, textvariable=self.window_x_var, width=8).pack(side=tk.LEFT)
        ttk.Label(pos_sub_frame, text=", ").pack(side=tk.LEFT)
        self.window_y_var = tk.StringVar(value=str(window_position[1]))
        ttk.Entry(pos_sub_frame, textvariable=self.window_y_var, width=8).pack(side=tk.LEFT)
        
        # 多窗口设置
        multi_window_frame = ttk.LabelFrame(window_frame, text="多窗口设置", padding=10)
        multi_window_frame.pack(fill=tk.X, padx=5, pady=5)
        
        multi_window = self.config_data.get("multi_window", {})
        
        self.multi_window_enabled_var = tk.BooleanVar(value=multi_window.get("enabled", True))
        ttk.Checkbutton(
            multi_window_frame, text="启用多窗口处理", 
            variable=self.multi_window_enabled_var
        ).grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=2)
        
        ttk.Label(multi_window_frame, text="每窗口联系人数:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.contacts_per_window_var = tk.StringVar(value=str(multi_window.get("contacts_per_window", 2)))
        ttk.Entry(multi_window_frame, textvariable=self.contacts_per_window_var, width=10).grid(row=1, column=1, padx=(10, 0), pady=2)
        
        ttk.Label(multi_window_frame, text="最大窗口数:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.max_windows_var = tk.StringVar(value=str(multi_window.get("max_windows", 5)))
        ttk.Entry(multi_window_frame, textvariable=self.max_windows_var, width=10).grid(row=2, column=1, padx=(10, 0), pady=2)
        
        ttk.Label(multi_window_frame, text="切换延迟 (秒):").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.switch_delay_var = tk.StringVar(value=str(multi_window.get("switch_delay", 3)))
        ttk.Entry(multi_window_frame, textvariable=self.switch_delay_var, width=10).grid(row=3, column=1, padx=(10, 0), pady=2)
    
    def create_safety_tab(self):
        """创建安全设置标签页"""
        safety_frame = ttk.Frame(self.notebook)
        self.notebook.add(safety_frame, text="安全设置")
        
        # 安全限制
        safety_limit_frame = ttk.LabelFrame(safety_frame, text="安全限制", padding=10)
        safety_limit_frame.pack(fill=tk.X, padx=5, pady=5)
        
        safety = self.config_data.get("safety", {})
        
        ttk.Label(safety_limit_frame, text="每小时最大操作数:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.max_operations_var = tk.StringVar(value=str(safety.get("max_operations_per_hour", 100)))
        ttk.Entry(safety_limit_frame, textvariable=self.max_operations_var, width=10).grid(row=0, column=1, padx=(10, 0), pady=2)
        
        ttk.Label(safety_limit_frame, text="最大运行时间 (小时):").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.max_runtime_var = tk.StringVar(value=str(safety.get("max_runtime_hours", 8)))
        ttk.Entry(safety_limit_frame, textvariable=self.max_runtime_var, width=10).grid(row=1, column=1, padx=(10, 0), pady=2)
        
        ttk.Label(safety_limit_frame, text="冷却时间 (秒):").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.cool_down_var = tk.StringVar(value=str(safety.get("cool_down_time", 300)))
        ttk.Entry(safety_limit_frame, textvariable=self.cool_down_var, width=10).grid(row=2, column=1, padx=(10, 0), pady=2)
    
    def create_buttons(self):
        """创建按钮"""
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Button(
            button_frame, text="确定", 
            command=self.on_ok
        ).pack(side=tk.RIGHT, padx=(5, 0))
        
        ttk.Button(
            button_frame, text="取消", 
            command=self.on_cancel
        ).pack(side=tk.RIGHT)
        
        ttk.Button(
            button_frame, text="重置为默认", 
            command=self.on_reset
        ).pack(side=tk.LEFT)
    
    def on_ok(self):
        """确定按钮事件"""
        try:
            # 收集配置数据
            self.collect_config_data()
            
            # 保存配置
            self.save_config()
            
            self.result = "ok"
            self.dialog.destroy()
            
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def on_cancel(self):
        """取消按钮事件"""
        self.result = "cancel"
        self.dialog.destroy()
    
    def on_reset(self):
        """重置按钮事件"""
        if messagebox.askyesno("确认", "确定要重置为默认配置吗？"):
            self.config_data = self.get_default_config()
            self.update_widgets()
    
    def collect_config_data(self):
        """收集配置数据"""
        # 基本设置
        self.config_data["excel_file"] = self.excel_file_var.get()
        self.config_data["batch_size"] = int(self.batch_size_var.get())
        self.config_data["retry_times"] = int(self.retry_times_var.get())
        
        # 时间设置
        self.config_data["delay_range"] = [
            float(self.delay_min_var.get()),
            float(self.delay_max_var.get())
        ]
        self.config_data["click_delay"] = [
            float(self.click_min_var.get()),
            float(self.click_max_var.get())
        ]
        
        # 窗口设置
        self.config_data["window_position"] = [
            int(self.window_x_var.get()),
            int(self.window_y_var.get())
        ]
        
        self.config_data["multi_window"] = {
            "enabled": self.multi_window_enabled_var.get(),
            "contacts_per_window": int(self.contacts_per_window_var.get()),
            "max_windows": int(self.max_windows_var.get()),
            "switch_delay": float(self.switch_delay_var.get())
        }
        
        # 安全设置
        self.config_data["safety"] = {
            "max_operations_per_hour": int(self.max_operations_var.get()),
            "max_runtime_hours": int(self.max_runtime_var.get()),
            "cool_down_time": int(self.cool_down_var.get())
        }
    
    def save_config(self):
        """保存配置到文件"""
        with open(self.config_file, "w", encoding="utf-8") as f:
            json.dump(self.config_data, f, ensure_ascii=False, indent=2)
    
    def update_widgets(self):
        """更新界面组件"""
        # 这里可以实现界面更新逻辑
        pass
    
    def show(self):
        """显示对话框并等待结果"""
        self.dialog.wait_window()
        return self.result


def show_config_dialog(parent, config_file: str = "config.json"):
    """显示配置对话框"""
    dialog = ConfigDialog(parent, config_file)
    return dialog.show()


if __name__ == "__main__":
    # 测试代码
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    result = show_config_dialog(root)
    print(f"对话框结果: {result}")
    
    root.destroy()
