#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信窗口管理模块
功能：获取微信窗口句柄、激活窗口、多微信窗口切换、窗口大小调整
主要功能：
- 智能识别微信主窗口（支持传统和新版Qt界面）
- 自动调整微信主窗口大小为指定尺寸（默认723x650像素）
- 窗口位置管理（移动到屏幕左上角）
- 多微信窗口切换和管理
- 配置驱动的窗口管理策略
"""

import win32gui
import win32con
import win32process
import time
import logging
import psutil
from typing import List, Dict, Optional

class WeChatWindowManager:
    """微信窗口管理器"""
    
    def __init__(self, config_path: str = "config.json", frequency_handler=None):
        self.logger = logging.getLogger(__name__)
        self.wechat_windows = []  # 存储所有微信窗口信息
        self.current_window_index = 0  # 当前活动窗口索引
        self.frequency_handler = frequency_handler  # 🆕 频率错误处理器引用
        self.window_class_names = [
            "WeChatMainWndForPC",  # 微信主窗口
            "ChatWnd",             # 聊天窗口
            "ContactManagerWindow" # 联系人管理窗口
        ]

        # 微信进程名称列表（支持多种微信版本）
        self.wechat_process_names = [
            "WeChat.exe",          # 标准微信进程名
            "Weixin.exe",          # 微信进程名（中文版）
            "WeChatApp.exe",       # 微信应用进程名
            "WeChatWin.exe",       # Windows微信进程名
            "微信.exe"             # 中文进程名
        ]

        # 窗口过滤配置
        self.window_filter_config = {
            "min_window_width": 200,      # 最小窗口宽度
            "min_window_height": 150,     # 最小窗口高度
            "min_window_area": 30000,     # 最小窗口面积 (200x150)
            "max_offscreen_distance": 100, # 允许的最大屏幕外距离
            "enable_size_filter": True,   # 启用大小过滤
            "enable_position_filter": True, # 启用位置过滤
            "debug_filtered_windows": True # 调试被过滤的窗口（默认开启用于测试）
        }

        # 加载配置
        self.config = self._load_config(config_path)
        self.window_management = self.config.get("window_management", {
            "auto_move_to_top_left": True,
            "move_main_window": True,
            "move_dialog_window": True
        })

    def _load_config(self, config_path: str) -> dict:
        """加载配置文件"""
        try:
            import json
            import os

            if not os.path.exists(config_path):
                self.logger.warning(f"⚠️ 配置文件不存在: {config_path}，使用默认配置")
                return {}

            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.logger.info(f"✅ 配置文件加载成功: {config_path}")
                return config
        except Exception as e:
            self.logger.error(f"❌ 配置文件加载失败: {e}")
            return {}

    def _get_process_name_by_hwnd(self, hwnd: int) -> Optional[str]:
        """通过窗口句柄获取进程名称"""
        try:
            # 获取窗口的进程ID
            _, process_id = win32process.GetWindowThreadProcessId(hwnd)

            # 使用psutil获取进程信息
            try:
                process = psutil.Process(process_id)
                process_name = process.name()
                self.logger.debug(f"窗口句柄 {hwnd} 对应进程: {process_name} (PID: {process_id})")
                return process_name
            except psutil.NoSuchProcess:
                self.logger.debug(f"进程 {process_id} 不存在或已结束")
                return None
            except psutil.AccessDenied:
                self.logger.debug(f"无法访问进程 {process_id} 的信息")
                return None

        except Exception as e:
            self.logger.debug(f"获取窗口 {hwnd} 的进程名称失败: {e}")
            return None

    def _is_wechat_process(self, process_name: str) -> bool:
        """验证进程名称是否为微信进程"""
        if not process_name:
            return False

        # 转换为小写进行比较，提高匹配准确性
        process_name_lower = process_name.lower()

        for wechat_process in self.wechat_process_names:
            if process_name_lower == wechat_process.lower():
                self.logger.debug(f"✅ 确认为微信进程: {process_name}")
                return True

        self.logger.debug(f"❌ 非微信进程: {process_name}")
        return False

    def _verify_window_process(self, hwnd: int) -> bool:
        """验证窗口是否属于微信进程"""
        try:
            process_name = self._get_process_name_by_hwnd(hwnd)
            if not process_name:
                self.logger.debug(f"无法获取窗口 {hwnd} 的进程信息")
                return False

            is_wechat = self._is_wechat_process(process_name)
            if is_wechat:
                self.logger.debug(f"✅ 窗口 {hwnd} 属于微信进程: {process_name}")
            else:
                self.logger.debug(f"❌ 窗口 {hwnd} 不属于微信进程: {process_name}")

            return is_wechat

        except Exception as e:
            self.logger.debug(f"验证窗口 {hwnd} 进程失败: {e}")
            return False

    def _is_valid_window_size(self, hwnd: int) -> bool:
        """验证窗口大小是否符合用户界面窗口的要求（增强版：支持最小化窗口恢复）"""
        try:
            if not self.window_filter_config.get("enable_size_filter", True):
                return True

            rect = win32gui.GetWindowRect(hwnd)
            width = rect[2] - rect[0]
            height = rect[3] - rect[1]
            area = width * height

            min_width = self.window_filter_config.get("min_window_width", 200)
            min_height = self.window_filter_config.get("min_window_height", 150)
            min_area = self.window_filter_config.get("min_window_area", 30000)

            size_valid = (width >= min_width and
                         height >= min_height and
                         area >= min_area)

            if not size_valid:
                # 检查是否为被最小化的微信窗口
                is_iconic = win32gui.IsIconic(hwnd)
                window_text = win32gui.GetWindowText(hwnd)
                class_name = win32gui.GetClassName(hwnd)

                # 如果是微信窗口且被最小化，尝试恢复后再判断
                if (window_text in ['微信', 'WeChat'] and
                    class_name == 'Qt51514QWindowIcon' and
                    (is_iconic or area < 10000)):  # 很小的窗口可能是最小化状态

                    self.logger.debug(f"🔄 检测到可能被最小化的微信窗口: {width}x{height}, 尝试恢复...")

                    try:
                        # 尝试恢复窗口
                        win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                        time.sleep(0.5)  # 等待窗口恢复

                        # 重新获取窗口大小
                        new_rect = win32gui.GetWindowRect(hwnd)
                        new_width = new_rect[2] - new_rect[0]
                        new_height = new_rect[3] - new_rect[1]
                        new_area = new_width * new_height

                        self.logger.debug(f"恢复后窗口大小: {new_width}x{new_height} (面积: {new_area})")

                        # 检查恢复后的大小
                        if new_width >= min_width and new_height >= min_height and new_area >= min_area:
                            self.logger.debug(f"✅ 窗口恢复后符合要求: {new_width}x{new_height}")
                            return True
                        else:
                            # 即使恢复后仍然很小，但如果是微信窗口，也接受（可能是特殊的微信窗口）
                            if new_width >= 100 and new_height >= 50:  # 降低标准
                                self.logger.debug(f"✅ 微信窗口恢复后接受（降低标准）: {new_width}x{new_height}")
                                return True

                    except Exception as e:
                        self.logger.debug(f"恢复窗口失败: {e}")

                if self.window_filter_config.get("debug_filtered_windows", False):
                    self.logger.debug(f"❌ 窗口 {hwnd} 大小不符合要求: {width}x{height} (面积: {area})")
                    self.logger.debug(f"   最小要求: {min_width}x{min_height} (面积: {min_area})")
                return False

            return True

        except Exception as e:
            self.logger.debug(f"验证窗口 {hwnd} 大小失败: {e}")
            return True  # 如果无法获取大小，不过滤

    def _is_valid_window_position(self, hwnd: int) -> bool:
        """验证窗口位置是否在有效范围内（增强版：支持屏幕外窗口恢复）"""
        try:
            if not self.window_filter_config.get("enable_position_filter", True):
                return True

            rect = win32gui.GetWindowRect(hwnd)
            left, top, right, bottom = rect

            # 检查窗口是否被移动到屏幕外很远的地方（通常是最小化的标志）
            is_far_offscreen = (abs(left) > 30000 or abs(top) > 30000 or
                               abs(right) > 30000 or abs(bottom) > 30000)

            if is_far_offscreen:
                # 对于微信窗口，尝试恢复被移动到屏幕外的窗口
                window_text = win32gui.GetWindowText(hwnd)
                class_name = win32gui.GetClassName(hwnd)

                if (window_text in ['微信', 'WeChat'] and
                    class_name == 'Qt51514QWindowIcon'):

                    self.logger.debug(f"🔄 检测到被移动到屏幕外的微信窗口: ({left}, {top}, {right}, {bottom})")

                    try:
                        # 尝试恢复窗口
                        win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                        time.sleep(0.3)

                        # 重新获取位置
                        new_rect = win32gui.GetWindowRect(hwnd)
                        new_left, new_top, new_right, new_bottom = new_rect

                        self.logger.debug(f"恢复后窗口位置: ({new_left}, {new_top}, {new_right}, {new_bottom})")

                        # 检查是否恢复到正常位置
                        new_is_offscreen = (abs(new_left) > 30000 or abs(new_top) > 30000)
                        if not new_is_offscreen:
                            self.logger.debug(f"✅ 微信窗口成功恢复到屏幕内")
                            return True
                        else:
                            # 即使仍在屏幕外，对于微信窗口也接受（可能是系统特性）
                            self.logger.debug(f"✅ 微信窗口接受（即使在屏幕外）")
                            return True

                    except Exception as e:
                        self.logger.debug(f"恢复屏幕外窗口失败: {e}")
                        # 对于微信窗口，即使恢复失败也接受
                        return True

                if self.window_filter_config.get("debug_filtered_windows", False):
                    self.logger.debug(f"❌ 窗口 {hwnd} 位置异常（被移动到屏幕外）: ({left}, {top}, {right}, {bottom})")
                return False

            return True

        except Exception as e:
            self.logger.debug(f"验证窗口 {hwnd} 位置失败: {e}")
            return True  # 如果无法获取位置，不过滤

    def _is_user_interface_window(self, hwnd: int) -> bool:
        """综合验证窗口是否为有效的用户界面窗口"""
        try:
            # 检查窗口大小
            if not self._is_valid_window_size(hwnd):
                return False

            # 检查窗口位置
            if not self._is_valid_window_position(hwnd):
                return False

            # 检查窗口状态
            is_visible = win32gui.IsWindowVisible(hwnd)
            is_enabled = win32gui.IsWindowEnabled(hwnd)

            if not (is_visible and is_enabled):
                if self.window_filter_config.get("debug_filtered_windows", False):
                    self.logger.debug(f"❌ 窗口 {hwnd} 状态异常: 可见={is_visible}, 启用={is_enabled}")
                return False

            return True

        except Exception as e:
            self.logger.debug(f"验证窗口 {hwnd} 用户界面状态失败: {e}")
            return True  # 如果验证失败，不过滤

    def find_all_wechat_windows(self) -> List[Dict]:
        """查找所有微信窗口"""
        self.logger.info("🔍 开始搜索所有微信窗口...")
        self.wechat_windows = []

        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                class_name = win32gui.GetClassName(hwnd)

                # 确保class_name不为None
                if class_name is None:
                    class_name = ""

                # 检查是否为微信窗口（包含进程验证）
                if self._is_wechat_window(hwnd, window_text, class_name):
                    # 🆕 检查窗口是否在黑名单中
                    if self._is_window_blacklisted(hwnd, window_text):
                        self.logger.warning(f"🚫 跳过黑名单窗口: {window_text} (句柄: {hwnd})")
                        return True  # 继续枚举其他窗口

                    # 获取进程信息用于记录
                    process_name = self._get_process_name_by_hwnd(hwnd)

                    # 更智能的主窗口判断
                    is_main = self._is_main_wechat_window_by_class_and_size(hwnd, window_text, class_name)

                    window_info = {
                        'hwnd': hwnd,
                        'title': window_text,
                        'class_name': class_name,
                        'process_name': process_name,
                        'rect': win32gui.GetWindowRect(hwnd),
                        'is_main': is_main
                    }
                    windows.append(window_info)
                    self.logger.info(f"✅ 找到微信窗口: {window_text} (句柄: {hwnd}, 进程: {process_name})")

            return True

        win32gui.EnumWindows(enum_windows_callback, self.wechat_windows)

        # 按主窗口优先排序
        self.wechat_windows.sort(key=lambda x: (not x['is_main'], x['hwnd']))

        self.logger.info(f"🎯 总共找到 {len(self.wechat_windows)} 个微信窗口")
        return self.wechat_windows

    def get_all_wechat_windows(self) -> List[Dict]:
        """获取所有微信窗口（兼容方法）"""
        return self.find_all_wechat_windows()
    
    def _is_wechat_window(self, hwnd: int, window_text: str, class_name: str) -> bool:
        """判断是否为微信窗口（增强版：包含进程验证）"""
        # 确保参数不为None
        if not window_text:
            window_text = ""
        if not class_name:
            class_name = ""

        # 第一步：进程验证（最重要的过滤条件）
        if not self._verify_window_process(hwnd):
            self.logger.debug(f"❌ 窗口 {hwnd} 进程验证失败: '{window_text}' (类名: '{class_name}')")
            return False

        # 第二步：用户界面窗口验证（过滤辅助窗口）
        if not self._is_user_interface_window(hwnd):
            self.logger.debug(f"❌ 窗口 {hwnd} 不是有效的用户界面窗口: '{window_text}' (类名: '{class_name}')")
            return False

        # 第三步：排除非微信应用程序（额外保护）
        exclude_keywords = [
            "Visual Studio Code", "VS Code", "VSCode",
            "Chrome", "Firefox", "Edge", "Safari",
            "Notepad", "记事本", "Word", "Excel", "PowerPoint",
            "PyCharm", "IntelliJ", "Eclipse", "Sublime",
            "Atom", "Brackets", "WebStorm", "PhpStorm",
            "Android Studio", "Xcode", "Unity",
            "OBS", "Photoshop", "Illustrator", "Premiere",
            "QQ", "钉钉", "企业微信", "飞书", "Slack",
            "Steam", "Epic Games", "Origin", "Uplay",
            "VMware", "VirtualBox", "Hyper-V",
            "cmd", "PowerShell", "Terminal", "命令提示符"
        ]

        # 如果窗口标题包含排除关键词，直接返回False
        for exclude_keyword in exclude_keywords:
            if exclude_keyword in window_text:
                self.logger.debug(f"❌ 窗口标题包含排除关键词 '{exclude_keyword}': {window_text}")
                return False

        # 第四步：检查类名（优先级最高）
        if class_name in self.window_class_names:
            self.logger.debug(f"✅ 窗口类名匹配微信窗口: {class_name}")

            # 🆕 关键修复：即使类名匹配，也要验证窗口大小，防止异常小窗口
            try:
                rect = win32gui.GetWindowRect(hwnd)
                width = rect[2] - rect[0]
                height = rect[3] - rect[1]
                area = width * height

                # 设置最小窗口大小阈值（排除残留的小窗口）
                min_width = 200
                min_height = 150
                min_area = 30000  # 200x150

                if width >= min_width and height >= min_height and area >= min_area:
                    self.logger.debug(f"✅ 窗口大小验证通过: {width}x{height}")
                    return True
                else:
                    self.logger.warning(f"🚫 过滤异常小微信窗口: {window_text} ({width}x{height}) - 可能是残留窗口")
                    return False
            except Exception as e:
                self.logger.debug(f"⚠️ 窗口大小验证失败: {e}")
                return True  # 如果无法获取大小，不过滤

        # 第五步：检查窗口标题中的微信关键词
        wechat_keywords = ["微信", "WeChat", "添加朋友", "通讯录"]
        title_match = any(keyword in window_text for keyword in wechat_keywords)

        # 如果标题匹配微信关键词，进一步验证
        if title_match:
            self.logger.debug(f"窗口标题包含微信关键词: '{window_text}'")

            # 🆕 关键修复：标题匹配时也要验证窗口大小
            try:
                rect = win32gui.GetWindowRect(hwnd)
                width = rect[2] - rect[0]
                height = rect[3] - rect[1]
                area = width * height

                # 设置最小窗口大小阈值
                min_width = 200
                min_height = 150
                min_area = 30000

                if width < min_width or height < min_height or area < min_area:
                    self.logger.warning(f"🚫 过滤异常小微信窗口: {window_text} ({width}x{height}) - 可能是残留窗口")
                    return False
            except Exception as e:
                self.logger.debug(f"⚠️ 窗口大小验证失败: {e}")

            # 确保是纯微信窗口标题，而不是包含微信的其他应用
            pure_wechat_titles = [
                "微信",
                "WeChat",
                "微信登录",
                "WeChat Login",
                "添加朋友",
                "Add Friends",
                "通讯录",
                "Contacts"
            ]

            # 检查是否为纯微信标题
            if window_text in pure_wechat_titles:
                self.logger.debug(f"✅ 窗口标题为纯微信标题: '{window_text}'")
                return True

            # 检查是否以微信开头且不包含其他应用标识
            if window_text.startswith("微信") or window_text.startswith("WeChat"):
                # 排除包含文件路径、编辑器标识等的标题
                exclude_patterns = ["-", "\\", "/", ".py", ".js", ".html", ".css", ".json", ".xml", ".txt"]
                if not any(pattern in window_text for pattern in exclude_patterns):
                    self.logger.debug(f"✅ 窗口标题以微信开头且无排除模式: '{window_text}'")
                    return True
                else:
                    self.logger.debug(f"❌ 窗口标题包含排除模式: '{window_text}'")

        self.logger.debug(f"❌ 窗口不符合微信窗口条件: '{window_text}' (类名: '{class_name}')")
        return False
    
    def get_current_window(self) -> Optional[Dict]:
        """获取当前活动的微信窗口"""
        if not self.wechat_windows:
            self.find_all_wechat_windows()
        
        if self.wechat_windows and 0 <= self.current_window_index < len(self.wechat_windows):
            return self.wechat_windows[self.current_window_index]
        return None
    
    def switch_to_next_window(self) -> Optional[Dict]:
        """切换到下一个微信窗口（增强版：确保窗口置顶显示，过滤黑名单窗口）"""
        if not self.wechat_windows:
            self.find_all_wechat_windows()

        if len(self.wechat_windows) <= 1:
            self.logger.warning("⚠️ 只有一个或没有微信窗口，无法切换")
            return self.get_current_window()

        # 记录当前窗口信息用于调试
        current_window = self.get_current_window()
        if current_window:
            self.logger.info(f"📍 当前窗口: {current_window['title']} (句柄: {current_window['hwnd']})")

        # 🆕 寻找下一个可用的窗口（跳过黑名单窗口）
        attempts = 0
        max_attempts = len(self.wechat_windows)

        while attempts < max_attempts:
            # 切换到下一个窗口
            self.current_window_index = (self.current_window_index + 1) % len(self.wechat_windows)
            next_window = self.wechat_windows[self.current_window_index]

            # 🆕 检查窗口是否在黑名单中
            if self._is_window_blacklisted(next_window['hwnd'], next_window['title']):
                self.logger.warning(f"🚫 跳过黑名单窗口: {next_window['title']} (句柄: {next_window['hwnd']})")
                attempts += 1
                continue

            self.logger.info(f"🔄 切换到微信窗口 {self.current_window_index + 1}/{len(self.wechat_windows)}: {next_window['title']}")
            self.logger.info(f"🎯 目标窗口详情: 句柄={next_window['hwnd']}, 类名={next_window.get('class_name', '未知')}")

            # 使用增强的窗口切换方法（专门优化）
            hwnd = next_window['hwnd']
            if self._enhanced_window_switch(hwnd, next_window):
                self.logger.info("✅ 窗口切换成功，已验证窗口状态")
                return next_window
            else:
                self.logger.error("❌ 窗口切换失败")
                attempts += 1
                continue

        # 🆕 如果所有窗口都在黑名单中或切换失败
        self.logger.error("❌ 所有可用窗口都在黑名单中或切换失败")
        return None

    def _enhanced_window_switch(self, hwnd: int, window_info: Dict) -> bool:
        """增强的窗口切换方法（智能容错版本）"""
        try:
            window_title = window_info.get('title', '未知窗口')
            self.logger.info(f"🚀 开始智能窗口切换: {window_title} (句柄: {hwnd})")

            # 第一步：验证目标窗口有效性
            if not win32gui.IsWindow(hwnd):
                self.logger.error(f"❌ 目标窗口句柄无效: {hwnd}")
                return False

            # 第二步：先关闭可能干扰的窗口
            self._close_interfering_windows()

            # 第三步：使用智能激活方法
            activation_success = self._safe_activate_window(hwnd)

            if not activation_success:
                self.logger.debug("🔄 标准激活方法未完全成功，尝试强制方法")
                # 使用强制方法作为备选
                activation_success = self._force_window_to_front(hwnd)

                if not activation_success:
                    # 即使强制激活失败，也检查窗口是否至少变为可见
                    if self._is_window_functionally_active(hwnd):
                        self.logger.info("✅ 窗口已成功切换（使用兼容模式）")
                        activation_success = True
                    else:
                        self.logger.warning("⚠️ 窗口切换遇到困难，但将继续尝试")
                        # 不直接返回False，而是继续尝试
                        activation_success = False

            # 第四步：智能验证切换结果（更宽松的验证标准）
            time.sleep(0.3)  # 减少等待时间

            verification_success = self._verify_window_switch_success(hwnd, window_info)

            if verification_success:
                self.logger.info("✅ 窗口切换验证成功")
            else:
                # 即使验证失败，如果窗口功能性激活，也认为成功
                if self._is_window_functionally_active(hwnd):
                    self.logger.info("✅ 窗口切换基本成功（功能性激活）")
                    verification_success = True
                else:
                    self.logger.warning("⚠️ 窗口切换验证失败，但继续执行后续操作")
                    # 不直接返回False，而是继续执行，让用户看到结果

            # 第五步：应用窗口管理（大小调整和位置管理）
            if activation_success:
                self.logger.debug("🔧 应用窗口管理功能...")
                window_management_success = self._apply_window_management(hwnd)
                if window_management_success:
                    self.logger.debug("✅ 窗口管理应用成功")
                else:
                    self.logger.debug("⚠️ 窗口管理应用失败，但不影响切换结果")

            # 第六步：延迟检测新闻提示框（无论验证结果如何都执行）
            self._schedule_delayed_popup_detection(window_info)

            # 返回结果：只要激活成功就认为切换成功
            final_result = activation_success

            if final_result:
                self.logger.info(f"✅ 窗口切换完成: {window_title}")
            else:
                self.logger.warning(f"⚠️ 窗口切换部分失败: {window_title}")

            return final_result

        except Exception as e:
            self.logger.error(f"❌ 增强窗口切换异常: {e}")
            return False

    def _close_interfering_windows(self):
        """关闭可能干扰窗口切换的窗口（增强版：包含新闻提示框检测）"""
        try:
            self.logger.debug("🔄 检查并关闭干扰窗口...")

            # 查找并关闭添加朋友窗口
            add_friend_hwnd = self.find_window_by_title("添加朋友")
            if add_friend_hwnd:
                self.logger.info("🔄 关闭添加朋友窗口以避免干扰")
                self.close_window(add_friend_hwnd)
                time.sleep(0.3)

            # 🆕 检测并关闭微信新闻提示框
            self._detect_and_close_news_popups()

        except Exception as e:
            self.logger.debug(f"关闭干扰窗口异常: {e}")

    def _detect_and_close_news_popups(self):
        """检测并关闭微信新闻提示框"""
        try:
            # 导入新闻提示框阻止器
            try:
                from .wechat_news_popup_blocker import WeChatNewsPopupBlocker

                # 创建阻止器实例（使用当前配置）
                blocker = WeChatNewsPopupBlocker(self.config)

                # 执行单次检测和阻止
                blocked_popups = blocker.detect_and_block_popups()

                if blocked_popups:
                    self.logger.info(f"✅ 成功阻止 {len(blocked_popups)} 个微信新闻提示框")
                    for popup in blocked_popups:
                        self.logger.debug(f"  - 已关闭: '{popup.title}' (类名: {popup.class_name})")
                else:
                    self.logger.debug("🔍 未检测到微信新闻提示框")

            except ImportError as e:
                self.logger.debug(f"无法导入新闻提示框阻止器: {e}")
            except Exception as e:
                self.logger.warning(f"⚠️ 新闻提示框检测异常: {e}")

        except Exception as e:
            self.logger.debug(f"检测新闻提示框异常: {e}")

    def _schedule_delayed_popup_detection(self, window_info: Dict):
        """安排延迟的新闻提示框检测（针对微信窗口激活后的弹窗）"""
        try:
            window_title = window_info.get('title', '未知')
            window_index = self.current_window_index + 1

            self.logger.info(f"📅 安排延迟新闻提示框检测: {window_title} (窗口 {window_index})")

            # 使用线程进行延迟检测，避免阻塞主流程
            import threading

            def delayed_detection():
                try:
                    # 等待1-3秒，让新闻提示框有时间弹出
                    detection_delays = [1.0, 2.0, 3.0]

                    for delay in detection_delays:
                        time.sleep(delay)

                        self.logger.debug(f"🔍 执行延迟检测 (延迟 {delay}s): {window_title}")

                        # 执行新闻提示框检测
                        self._detect_and_close_news_popups()

                        # 如果是微信窗口2或后续窗口，进行额外检测
                        if window_index >= 2:
                            self.logger.info(f"🎯 微信窗口 {window_index} 额外新闻提示框检测")
                            time.sleep(0.5)
                            self._detect_and_close_news_popups()

                    self.logger.debug(f"✅ 延迟新闻提示框检测完成: {window_title}")

                except Exception as e:
                    self.logger.error(f"❌ 延迟检测异常: {e}")

            # 启动延迟检测线程
            detection_thread = threading.Thread(target=delayed_detection, daemon=True)
            detection_thread.start()

        except Exception as e:
            self.logger.error(f"❌ 安排延迟检测异常: {e}")

    def enable_news_popup_blocking(self, enabled: bool = True):
        """启用或禁用新闻提示框阻止功能"""
        try:
            if not hasattr(self, 'config'):
                self.config = {}

            if 'news_popup_blocker' not in self.config:
                self.config['news_popup_blocker'] = {}

            self.config['news_popup_blocker']['enabled'] = enabled

            status = "启用" if enabled else "禁用"
            self.logger.info(f"📰 新闻提示框阻止功能已{status}")

        except Exception as e:
            self.logger.error(f"❌ 设置新闻提示框阻止功能异常: {e}")

    def block_news_popups_now(self) -> int:
        """立即检测并阻止新闻提示框（手动触发）"""
        try:
            self.logger.info("🚀 手动触发新闻提示框检测...")

            # 导入新闻提示框阻止器
            try:
                from .wechat_news_popup_blocker import WeChatNewsPopupBlocker

                # 创建阻止器实例
                blocker = WeChatNewsPopupBlocker(self.config)

                # 执行检测和阻止
                blocked_popups = blocker.detect_and_block_popups()

                if blocked_popups:
                    self.logger.info(f"✅ 手动检测成功阻止 {len(blocked_popups)} 个新闻提示框")
                    return len(blocked_popups)
                else:
                    self.logger.info("🔍 手动检测未发现新闻提示框")
                    return 0

            except ImportError as e:
                self.logger.error(f"❌ 无法导入新闻提示框阻止器: {e}")
                return 0

        except Exception as e:
            self.logger.error(f"❌ 手动检测新闻提示框异常: {e}")
            return 0

    def _safe_activate_window(self, hwnd: int) -> bool:
        """安全的窗口激活方法（智能多策略版本）"""
        try:
            self.logger.debug(f"🎯 智能安全激活窗口: {hwnd}")

            # 第一步：窗口状态预检查和修复
            if not self._prepare_window_for_activation(hwnd):
                self.logger.warning("⚠️ 窗口状态预处理失败")
                return False

            # 第二步：尝试多种激活策略（按成功率排序）
            activation_strategies = [
                ("温和激活", self._gentle_activation_strategy),
                ("增强激活", self._enhanced_activation_strategy),
                ("强制激活", self._force_activation_strategy),
                ("系统级激活", self._system_level_activation_strategy)
            ]

            for strategy_name, strategy_func in activation_strategies:
                self.logger.debug(f"🔄 尝试{strategy_name}策略...")

                try:
                    if strategy_func(hwnd):
                        # 验证激活结果
                        if self._verify_activation_success(hwnd, strategy_name):
                            self.logger.info(f"✅ {strategy_name}策略成功")
                            return True
                        else:
                            self.logger.debug(f"⚠️ {strategy_name}策略激活但验证失败，尝试下一策略")
                    else:
                        self.logger.debug(f"❌ {strategy_name}策略失败")

                except Exception as e:
                    self.logger.debug(f"❌ {strategy_name}策略异常: {e}")
                    continue

            # 所有策略都失败，但检查窗口是否至少变为可见状态
            if self._is_window_functionally_active(hwnd):
                self.logger.info("✅ 虽然完全激活失败，但窗口已达到功能性激活状态")
                return True

            self.logger.warning("⚠️ 所有激活策略都失败")
            return False

        except Exception as e:
            self.logger.error(f"❌ 安全激活窗口异常: {e}")
            return False

    def _prepare_window_for_activation(self, hwnd: int) -> bool:
        """为窗口激活做预处理"""
        try:
            # 检查窗口有效性
            if not win32gui.IsWindow(hwnd):
                self.logger.error(f"❌ 窗口句柄无效: {hwnd}")
                return False

            # 如果窗口最小化，先还原
            if win32gui.IsIconic(hwnd):
                self.logger.debug("🔄 还原最小化窗口")
                win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                time.sleep(0.3)

            # 确保窗口可见
            if not win32gui.IsWindowVisible(hwnd):
                self.logger.debug("🔄 显示不可见窗口")
                win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
                time.sleep(0.2)

            # 确保窗口启用
            if not win32gui.IsWindowEnabled(hwnd):
                self.logger.debug("🔄 启用被禁用的窗口")
                win32gui.EnableWindow(hwnd, True)
                time.sleep(0.1)

            return True

        except Exception as e:
            self.logger.debug(f"窗口预处理失败: {e}")
            return False

    def _gentle_activation_strategy(self, hwnd: int) -> bool:
        """温和激活策略 - 使用标准API"""
        try:
            # 先尝试设置为活动窗口
            try:
                win32gui.SetActiveWindow(hwnd)
                time.sleep(0.1)
            except Exception:
                pass  # 忽略SetActiveWindow的失败

            # 再尝试设置为前台窗口
            result = win32gui.SetForegroundWindow(hwnd)
            time.sleep(0.2)

            return bool(result)

        except Exception as e:
            self.logger.debug(f"温和激活策略异常: {e}")
            return False

    def _enhanced_activation_strategy(self, hwnd: int) -> bool:
        """增强激活策略 - 使用线程输入附加"""
        try:
            return self._try_enhanced_set_foreground_window(hwnd)
        except Exception as e:
            self.logger.debug(f"增强激活策略异常: {e}")
            return False

    def _force_activation_strategy(self, hwnd: int) -> bool:
        """强制激活策略 - 使用Z-order操作"""
        try:
            # 设置窗口到Z-order顶部
            win32gui.SetWindowPos(
                hwnd, win32con.HWND_TOP,
                0, 0, 0, 0,
                win32con.SWP_NOMOVE | win32con.SWP_NOSIZE | win32con.SWP_SHOWWINDOW
            )
            time.sleep(0.1)

            # 再次尝试前台激活
            result = win32gui.SetForegroundWindow(hwnd)
            time.sleep(0.2)

            return bool(result)

        except Exception as e:
            self.logger.debug(f"强制激活策略异常: {e}")
            return False

    def _system_level_activation_strategy(self, hwnd: int) -> bool:
        """系统级激活策略 - 临时置顶方法"""
        try:
            # 临时设置为最顶层
            win32gui.SetWindowPos(
                hwnd, win32con.HWND_TOPMOST,
                0, 0, 0, 0,
                win32con.SWP_NOMOVE | win32con.SWP_NOSIZE | win32con.SWP_SHOWWINDOW
            )
            time.sleep(0.1)

            # 尝试激活
            win32gui.SetForegroundWindow(hwnd)
            time.sleep(0.1)

            # 取消永久置顶
            win32gui.SetWindowPos(
                hwnd, win32con.HWND_NOTOPMOST,
                0, 0, 0, 0,
                win32con.SWP_NOMOVE | win32con.SWP_NOSIZE
            )
            time.sleep(0.1)

            return True

        except Exception as e:
            self.logger.debug(f"系统级激活策略异常: {e}")
            return False

    def _verify_activation_success(self, hwnd: int, strategy_name: str) -> bool:
        """验证激活是否成功（智能验证）"""
        try:
            time.sleep(0.2)  # 等待激活完成

            current_foreground = win32gui.GetForegroundWindow()
            is_visible = win32gui.IsWindowVisible(hwnd)
            is_iconic = win32gui.IsIconic(hwnd)
            is_enabled = win32gui.IsWindowEnabled(hwnd)

            # 完美激活：窗口成为前台窗口
            if current_foreground == hwnd and is_visible and not is_iconic and is_enabled:
                self.logger.debug(f"✅ {strategy_name} - 完美激活成功")
                return True

            # 良好激活：窗口可见且非最小化（即使不是前台）
            if is_visible and not is_iconic and is_enabled:
                self.logger.debug(f"✅ {strategy_name} - 良好激活成功（窗口可见）")
                return True

            # 基础激活：窗口至少变为可见
            if is_visible and is_enabled:
                self.logger.debug(f"⚠️ {strategy_name} - 基础激活成功（最小要求）")
                return True

            self.logger.debug(f"❌ {strategy_name} - 激活验证失败")
            return False

        except Exception as e:
            self.logger.debug(f"激活验证异常: {e}")
            return False

    def _is_window_functionally_active(self, hwnd: int) -> bool:
        """检查窗口是否达到功能性激活状态"""
        try:
            is_visible = bool(win32gui.IsWindowVisible(hwnd))
            is_iconic = bool(win32gui.IsIconic(hwnd))
            is_enabled = bool(win32gui.IsWindowEnabled(hwnd))

            # 功能性激活的最低要求：可见、非最小化、启用
            return is_visible and not is_iconic and is_enabled

        except Exception as e:
            self.logger.debug(f"检查功能性激活状态异常: {e}")
            return False

    def _verify_window_switch_success(self, hwnd: int, window_info: Dict) -> bool:
        """验证窗口切换是否成功"""
        try:
            # 检查基本状态
            is_visible = win32gui.IsWindowVisible(hwnd)
            is_iconic = win32gui.IsIconic(hwnd)
            is_enabled = win32gui.IsWindowEnabled(hwnd)
            current_foreground = win32gui.GetForegroundWindow()

            self.logger.debug(f"窗口切换验证: 可见={is_visible}, 非最小化={not is_iconic}, 启用={is_enabled}")
            self.logger.debug(f"前台窗口: 目标={hwnd}, 当前={current_foreground}")

            # 基础状态必须正常
            if not (is_visible and not is_iconic and is_enabled):
                self.logger.warning("⚠️ 窗口基础状态异常")
                return False

            # 检查是否为前台窗口（理想情况）
            if current_foreground == hwnd:
                self.logger.info("✅ 窗口已成功切换到前台")
                return True

            # 即使不是前台窗口，但如果窗口可见且非最小化，也认为切换成功
            # （某些情况下系统可能不允许完全夺取焦点）
            if is_visible and not is_iconic:
                self.logger.info("✅ 窗口切换成功（窗口可见且非最小化）")
                return True

            return False

        except Exception as e:
            self.logger.error(f"❌ 验证窗口切换失败: {e}")
            return False
    
    def activate_window(self, hwnd: int) -> bool:
        """激活指定窗口并移动到左上角（增强版）"""
        try:
            self.logger.info(f"🎯 激活微信窗口 (句柄: {hwnd}) - 增强版")

            # 检查窗口是否存在
            if not win32gui.IsWindow(hwnd):
                self.logger.error(f"❌ 窗口句柄无效: {hwnd}")
                return False

            # 使用增强的激活方法（包含置顶功能）
            activation_success = self._enhanced_activate_window(hwnd)

            if activation_success:
                self.logger.info("✅ 微信窗口激活成功")
            else:
                self.logger.warning("⚠️ 窗口激活失败，尝试强化设置")

                # 如果常规激活失败，使用强化设置
                if self.ensure_window_always_visible(hwnd):
                    self.logger.info("✅ 窗口强化设置成功")
                    activation_success = True
                else:
                    self.logger.warning("⚠️ 窗口强化设置也失败，继续尝试移动窗口")

            # 检查窗口类型并执行相应操作
            window_info = self.get_window_info(hwnd)

            if self.window_management.get("auto_move_to_top_left", True):
                # 智能识别微信主窗口
                is_main_window = self._is_main_wechat_window(hwnd, window_info)

                if is_main_window and self.window_management.get("move_main_window", True):
                    self.logger.info("🏠 检测到微信主窗口，执行大小调整和移动操作...")

                    # 从配置获取窗口大小设置
                    window_size = self.window_management.get("main_window_size", {"width": 723, "height": 650})
                    target_width = window_size.get("width", 723)
                    target_height = window_size.get("height", 650)

                    # 检查是否启用窗口大小调整
                    if self.window_management.get("resize_main_window", True):
                        self.logger.info(f"🔧 调整微信主窗口大小为 {target_width}x{target_height} 像素...")
                        resize_success = self.resize_main_window(hwnd, target_width, target_height)
                    else:
                        self.logger.info("ℹ️ 窗口大小调整已禁用，仅移动位置")
                        resize_success = self._move_window_to_top_left(hwnd)
                    if resize_success:
                        self.logger.info("✅ 微信主窗口大小调整成功")
                    else:
                        self.logger.warning("⚠️ 微信主窗口大小调整失败，尝试仅移动位置")
                        # 如果大小调整失败，至少尝试移动到左上角
                        if self._move_window_to_top_left(hwnd):
                            self.logger.info("📍 微信主窗口位置移动成功")
                        else:
                            self.logger.warning("⚠️ 微信主窗口位置移动失败")
                elif not is_main_window:
                    # 🆕 对于未被识别为主窗口的微信窗口，也执行移动操作
                    title = window_info.get('title', '')
                    class_name = window_info.get('class_name', '')

                    # 检查是否为微信相关窗口
                    is_wechat_window = (
                        ("微信" in title or "WeChat" in title) or
                        class_name == "WeChatMainWndForPC" or
                        ("Qt" in class_name and "QWindow" in class_name)
                    )

                    if is_wechat_window:
                        self.logger.info(f"🔄 检测到微信窗口但未识别为主窗口: '{title}' (类名: {class_name})")
                        self.logger.info("📍 执行位置移动操作以确保一致性...")

                        if self._move_window_to_top_left(hwnd):
                            self.logger.info("✅ 微信窗口位置移动成功")
                        else:
                            self.logger.warning("⚠️ 微信窗口位置移动失败")
                elif self.window_management.get("move_dialog_window", True):
                    self.logger.info("💬 检测到微信对话窗口，执行移动操作...")
                    if self._move_window_to_top_left(hwnd):
                        self.logger.info("📍 微信对话窗口移动成功")
                    else:
                        self.logger.warning("⚠️ 微信对话窗口移动失败")
            else:
                self.logger.info("ℹ️ 根据配置跳过窗口移动和大小调整")

            # 增强的最终验证和置顶确保（在窗口管理完成后）
            if activation_success:
                # 窗口管理完成后，进行最终的置顶确认
                self.logger.debug("🔝 执行最终置顶确认...")
                try:
                    # 确保窗口在最顶层（不影响大小和位置）
                    win32gui.SetWindowPos(
                        hwnd, win32con.HWND_TOP,
                        0, 0, 0, 0,
                        win32con.SWP_NOMOVE | win32con.SWP_NOSIZE | win32con.SWP_SHOWWINDOW
                    )
                    time.sleep(0.2)
                except Exception as e:
                    self.logger.debug(f"最终置顶确认失败: {e}")

                # 使用增强的验证方法
                if self._verify_window_is_front(hwnd):
                    self.logger.info("✅ 窗口激活、管理和置顶验证成功")
                    return True
                else:
                    # 如果验证失败，但窗口可见且非最小化，也认为基本成功
                    try:
                        is_visible = win32gui.IsWindowVisible(hwnd)
                        is_iconic = win32gui.IsIconic(hwnd)
                        if is_visible and not is_iconic:
                            self.logger.info("✅ 窗口激活基本成功（可见且非最小化）")
                            return True
                        else:
                            self.logger.warning("⚠️ 窗口激活未完全成功")
                            return False
                    except Exception as e:
                        self.logger.debug(f"窗口状态检查失败: {e}")
                        return False

            return activation_success

        except Exception as e:
            self.logger.error(f"❌ 激活窗口失败: {e}")
            return False

    def _force_window_to_front(self, hwnd: int) -> bool:
        """强制将窗口置于最前台（兼容模式）"""
        try:
            self.logger.debug(f"� 使用兼容模式激活窗口: {hwnd}")

            # 第一步：基础窗口状态检查和恢复
            if not win32gui.IsWindow(hwnd):
                self.logger.error(f"❌ 窗口句柄无效: {hwnd}")
                return False

            # 如果窗口被最小化，先恢复
            if win32gui.IsIconic(hwnd):
                self.logger.debug("🔄 恢复最小化窗口...")
                win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                time.sleep(0.3)

            # 确保窗口可见
            if not win32gui.IsWindowVisible(hwnd):
                self.logger.debug("🔄 显示窗口...")
                win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
                time.sleep(0.2)

            # 第二步：强制置顶（使用 HWND_TOP 而不是 HWND_TOPMOST）
            self.logger.debug("🔝 设置窗口到 Z-order 顶部...")
            try:
                result = win32gui.SetWindowPos(
                    hwnd,
                    win32con.HWND_TOP,  # 置于所有窗口之上，但不是永久置顶
                    0, 0, 0, 0,
                    win32con.SWP_NOMOVE | win32con.SWP_NOSIZE | win32con.SWP_SHOWWINDOW
                )
                self.logger.debug(f"SetWindowPos(HWND_TOP) 返回: {result}")
            except Exception as e:
                self.logger.debug(f"SetWindowPos 失败: {e}")

            # 第三步：激活窗口
            self.logger.debug("🎯 激活窗口...")
            activation_success = self._try_enhanced_set_foreground_window(hwnd)

            # 第四步：额外的强制置顶（如果需要）
            if not activation_success:
                self.logger.debug("🔝 尝试临时置顶...")
                try:
                    # 临时设置为 TOPMOST，然后立即取消
                    win32gui.SetWindowPos(
                        hwnd, win32con.HWND_TOPMOST,
                        0, 0, 0, 0,
                        win32con.SWP_NOMOVE | win32con.SWP_NOSIZE
                    )
                    time.sleep(0.1)
                    win32gui.SetWindowPos(
                        hwnd, win32con.HWND_NOTOPMOST,
                        0, 0, 0, 0,
                        win32con.SWP_NOMOVE | win32con.SWP_NOSIZE
                    )
                    self.logger.debug("✅ 临时置顶策略执行完成")
                except Exception as e:
                    self.logger.debug(f"临时置顶失败: {e}")

            # 第五步：执行窗口管理（大小调整和位置管理）
            self.logger.debug("🔧 执行窗口管理功能...")
            self._apply_window_management(hwnd)

            # 第六步：最终验证
            time.sleep(0.3)
            return self._verify_window_is_front(hwnd)

        except Exception as e:
            self.logger.error(f"❌ 强制窗口置前失败: {e}")
            return False

    def _verify_window_is_front(self, hwnd: int) -> bool:
        """验证窗口是否真正在最前台"""
        try:
            # 检查基本状态
            is_visible = win32gui.IsWindowVisible(hwnd)
            is_iconic = win32gui.IsIconic(hwnd)
            is_enabled = win32gui.IsWindowEnabled(hwnd)
            current_foreground = win32gui.GetForegroundWindow()

            self.logger.debug(f"窗口状态验证: 可见={is_visible}, 非最小化={not is_iconic}, 启用={is_enabled}")
            self.logger.debug(f"前台窗口: 目标={hwnd}, 当前={current_foreground}, 匹配={current_foreground == hwnd}")

            # 基础状态检查
            if not (is_visible and not is_iconic and is_enabled):
                self.logger.warning("⚠️ 窗口基础状态异常")
                return False

            # 前台窗口检查
            if current_foreground == hwnd:
                self.logger.info("✅ 窗口已成功置于最前台")
                return True

            # 即使不是前台窗口，但如果窗口可见且非最小化，也可能是成功的
            # （某些情况下系统可能不允许完全夺取焦点）
            if is_visible and not is_iconic:
                self.logger.info("✅ 窗口可见且非最小化（部分成功）")
                return True

            self.logger.warning("⚠️ 窗口未能成功置于前台")
            return False

        except Exception as e:
            self.logger.error(f"❌ 验证窗口前台状态失败: {e}")
            return False

    def _apply_window_management(self, hwnd: int) -> bool:
        """应用窗口管理功能（大小调整和位置管理）"""
        try:
            if not self.window_management.get("auto_move_to_top_left", True):
                self.logger.debug("ℹ️ 窗口管理功能已禁用")
                return True

            # 获取窗口信息
            window_info = self.get_window_info(hwnd)

            # 智能识别微信主窗口
            is_main_window = self._is_main_wechat_window(hwnd, window_info)

            if is_main_window and self.window_management.get("move_main_window", True):
                self.logger.debug("🏠 应用微信主窗口管理...")

                # 从配置获取窗口大小设置
                window_size = self.window_management.get("main_window_size", {"width": 723, "height": 650})
                target_width = window_size.get("width", 723)
                target_height = window_size.get("height", 650)

                # 检查是否启用窗口大小调整
                if self.window_management.get("resize_main_window", True):
                    self.logger.debug(f"🔧 调整微信主窗口大小为 {target_width}x{target_height} 像素...")
                    resize_success = self.resize_main_window(hwnd, target_width, target_height)
                    if resize_success:
                        self.logger.debug("✅ 微信主窗口大小调整成功")
                        return True
                    else:
                        self.logger.debug("⚠️ 微信主窗口大小调整失败，尝试仅移动位置")
                        # 如果大小调整失败，至少尝试移动到左上角
                        return self._move_window_to_top_left(hwnd)
                else:
                    self.logger.debug("ℹ️ 窗口大小调整已禁用，仅移动位置")
                    return self._move_window_to_top_left(hwnd)

            elif self.window_management.get("move_dialog_window", True):
                self.logger.debug("💬 应用微信对话窗口管理...")
                return self._move_window_to_top_left(hwnd)
            else:
                self.logger.debug("ℹ️ 根据配置跳过窗口管理")
                return True

        except Exception as e:
            self.logger.debug(f"❌ 应用窗口管理失败: {e}")
            return False

    def apply_window_management(self, hwnd: int) -> bool:
        """应用窗口管理功能（公共方法）"""
        return self._apply_window_management(hwnd)

    def _is_window_blacklisted(self, hwnd: int, title: str) -> bool:
        """检查窗口是否在黑名单中

        Args:
            hwnd: 窗口句柄
            title: 窗口标题

        Returns:
            bool: 如果在黑名单中返回True
        """
        try:
            # 如果没有频率错误处理器引用，则不进行黑名单检查
            if not self.frequency_handler:
                return False

            # 检查窗口是否在黑名单中
            is_blacklisted = self.frequency_handler.is_window_blacklisted(hwnd, title)

            if is_blacklisted:
                # 获取黑名单信息用于日志记录
                blacklist_info = self.frequency_handler.get_window_blacklist_info(hwnd, title)
                if blacklist_info:
                    self.logger.info(f"🚫 窗口在黑名单中: {title}")
                    self.logger.info(f"📋 错误类型: {blacklist_info.get('error_type', 'unknown')}")
                    self.logger.info(f"📝 错误信息: {blacklist_info.get('error_message', 'unknown')}")
                    self.logger.info(f"⏰ 添加时间: {blacklist_info.get('added_time', 'unknown')}")

            return is_blacklisted

        except Exception as e:
            self.logger.error(f"❌ 检查窗口黑名单状态失败: {e}")
            return False  # 出错时不阻止窗口使用

    def set_frequency_handler(self, frequency_handler):
        """设置频率错误处理器引用

        Args:
            frequency_handler: FrequencyErrorHandler实例
        """
        self.frequency_handler = frequency_handler
        self.logger.info("✅ 已设置频率错误处理器引用")

    def _enhanced_activate_window(self, hwnd: int) -> bool:
        """增强的窗口激活方法，使用成功验证的策略"""
        try:
            # 策略1: 如果窗口最小化，先还原（成功方案）
            if win32gui.IsIconic(hwnd):
                self.logger.info("🔄 窗口被最小化，正在还原...")
                result = win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                self.logger.debug(f"ShowWindow(SW_RESTORE) 返回: {result}")
                time.sleep(0.3)

            # 策略2: 确保窗口可见（成功方案）
            if not win32gui.IsWindowVisible(hwnd):
                self.logger.info("🔄 窗口不可见，正在显示...")
                result = win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
                self.logger.debug(f"ShowWindow(SW_SHOW) 返回: {result}")
                time.sleep(0.2)

            # 策略3: 使用增强的激活方法（包含置顶逻辑）
            self.logger.debug("🔄 尝试激活方法 1 - 增强版SetForegroundWindow + 置顶")

            # 先尝试设置 Z-order
            try:
                win32gui.SetWindowPos(
                    hwnd, win32con.HWND_TOP,
                    0, 0, 0, 0,
                    win32con.SWP_NOMOVE | win32con.SWP_NOSIZE | win32con.SWP_SHOWWINDOW
                )
                self.logger.debug("✅ Z-order 设置完成")
            except Exception as e:
                self.logger.debug(f"Z-order 设置失败: {e}")

            if self._try_enhanced_set_foreground_window(hwnd):
                # 验证激活是否成功
                time.sleep(0.2)
                current_hwnd = win32gui.GetForegroundWindow()
                if current_hwnd == hwnd:
                    self.logger.info("✅ 激活方法 1 成功")
                    return True
                else:
                    # 检查窗口是否至少变为可见和非最小化
                    if win32gui.IsWindowVisible(hwnd) and not win32gui.IsIconic(hwnd):
                        self.logger.info("✅ 激活方法 1 部分成功（窗口可见）")
                        return True

            # 策略4: 如果常规方法失败，尝试临时置顶策略
            self.logger.debug("🔄 尝试临时置顶策略")
            try:
                # 临时设置为最顶层
                win32gui.SetWindowPos(
                    hwnd, win32con.HWND_TOPMOST,
                    0, 0, 0, 0,
                    win32con.SWP_NOMOVE | win32con.SWP_NOSIZE
                )
                time.sleep(0.1)

                # 尝试再次激活
                self._try_enhanced_set_foreground_window(hwnd)
                time.sleep(0.1)

                # 取消永久置顶，但保持在顶部
                win32gui.SetWindowPos(
                    hwnd, win32con.HWND_NOTOPMOST,
                    0, 0, 0, 0,
                    win32con.SWP_NOMOVE | win32con.SWP_NOSIZE
                )

                self.logger.debug("✅ 临时置顶策略执行完成")

                # 验证结果
                time.sleep(0.2)
                if win32gui.IsWindowVisible(hwnd) and not win32gui.IsIconic(hwnd):
                    self.logger.info("✅ 临时置顶策略成功")
                    return True

            except Exception as e:
                self.logger.debug(f"临时置顶策略失败: {e}")

            # 最终验证：即使前台窗口不是目标窗口，但如果窗口可见且非最小化，也认为成功
            if win32gui.IsWindowVisible(hwnd) and not win32gui.IsIconic(hwnd):
                self.logger.info("✅ 窗口激活成功（窗口可见且非最小化）")
                return True

            self.logger.warning("⚠️ 窗口激活失败")
            return False

        except Exception as e:
            self.logger.error(f"❌ 增强激活失败: {e}")
            return False

    def _try_enhanced_set_foreground_window(self, hwnd: int) -> bool:
        """增强版SetForegroundWindow，绕过系统限制"""
        try:
            import win32process
            import win32api

            # 获取当前线程和目标窗口线程ID
            current_thread_id = win32api.GetCurrentThreadId()
            target_thread_id = win32process.GetWindowThreadProcessId(hwnd)[0]

            self.logger.debug(f"当前线程ID: {current_thread_id}, 目标线程ID: {target_thread_id}")

            # 如果不是同一线程，尝试附加输入
            attached = False
            if current_thread_id != target_thread_id:
                try:
                    win32process.AttachThreadInput(current_thread_id, target_thread_id, True)
                    attached = True
                    self.logger.debug("🔗 已附加线程输入")
                except Exception as e:
                    self.logger.debug(f"线程输入附加失败: {e}")

            try:
                # 尝试激活窗口
                result = win32gui.SetForegroundWindow(hwnd)
                self.logger.debug(f"SetForegroundWindow 返回: {result}")
                time.sleep(0.1)

                # 尝试设置活动窗口
                try:
                    win32gui.SetActiveWindow(hwnd)
                    self.logger.debug("SetActiveWindow 调用成功")
                except Exception as e:
                    self.logger.debug(f"SetActiveWindow 失败: {e}")

                return True

            finally:
                # 分离线程输入
                if attached:
                    try:
                        win32process.AttachThreadInput(current_thread_id, target_thread_id, False)
                        self.logger.debug("🔗 已分离线程输入")
                    except Exception as e:
                        self.logger.debug(f"线程输入分离失败: {e}")

        except Exception as e:
            self.logger.debug(f"增强版SetForegroundWindow失败: {e}")
            return False



    def resize_main_window(self, hwnd: int, width: int = 723, height: int = 650) -> bool:
        """调整微信主窗口大小为指定尺寸并移动到配置位置（使用成功的验证机制）"""
        try:
            # 从配置获取目标位置
            target_position = self.config.get("window_position", [0, 0])
            target_x, target_y = target_position[0], target_position[1]

            self.logger.info(f"🔧 开始调整微信主窗口大小为 {width}x{height} 像素，位置为 ({target_x}, {target_y})...")

            # 获取窗口当前位置和大小
            rect = win32gui.GetWindowRect(hwnd)
            current_left, current_top, current_right, current_bottom = rect
            current_width = current_right - current_left
            current_height = current_bottom - current_top

            self.logger.info(f"📏 当前窗口位置: ({current_left}, {current_top}), 大小: {current_width}x{current_height}")
            self.logger.info(f"🎯 目标位置: ({target_x}, {target_y}), 目标大小: {width}x{height}")

            # 检查窗口是否已经是目标大小和位置
            if (abs(current_left - target_x) <= 10 and abs(current_top - target_y) <= 10 and
                abs(current_width - width) <= 5 and abs(current_height - height) <= 5):
                self.logger.info("✅ 窗口已经是目标大小和位置，无需调整")
                return True

            # 注意：MoveWindow 和 SetWindowPos API 在某些情况下会报告失败但实际成功
            # 因此我们直接使用验证机制来检查是否达到目标
            self.logger.info("🔄 尝试调整窗口大小和位置...")

            # 尝试使用 MoveWindow API（即使可能报告失败）
            try:
                win32gui.MoveWindow(hwnd, target_x, target_y, width, height, True)
                time.sleep(0.3)  # 等待窗口调整完成
            except Exception as e:
                self.logger.debug(f"MoveWindow API 异常: {e}")

            # 最终验证：检查窗口是否实际已调整（这是成功的关键）
            final_verification = self._verify_window_size_and_position(hwnd, target_x, target_y, width, height, tolerance=15)
            if final_verification:
                self.logger.info("✅ 虽然API可能报告失败，但窗口实际已调整到目标大小和位置")
                return True

            self.logger.warning("⚠️ 窗口大小和位置调整未达到预期效果")
            return False

        except Exception as e:
            self.logger.error(f"❌ 调整窗口大小失败: {e}")
            return False

    def _verify_window_size(self, hwnd: int, target_width: int, target_height: int, tolerance: int = 10) -> bool:
        """验证窗口是否已调整到目标大小（兼容旧版本调用）"""
        # 从配置获取目标位置
        target_position = self.config.get("window_position", [0, 0])
        target_x, target_y = target_position[0], target_position[1]

        return self._verify_window_size_and_position(hwnd, target_x, target_y, target_width, target_height, tolerance)

    def _verify_window_size_and_position(self, hwnd: int, target_x: int, target_y: int, target_width: int, target_height: int, tolerance: int = 10) -> bool:
        """验证窗口大小和位置是否符合预期"""
        try:
            rect = win32gui.GetWindowRect(hwnd)
            left, top, right, bottom = rect
            actual_width = right - left
            actual_height = bottom - top

            width_diff = abs(actual_width - target_width)
            height_diff = abs(actual_height - target_height)
            position_x_diff = abs(left - target_x)
            position_y_diff = abs(top - target_y)

            self.logger.info(f"📊 验证结果 - 当前位置: ({left}, {top}), 实际大小: {actual_width}x{actual_height}")
            self.logger.info(f"📊 目标位置: ({target_x}, {target_y}), 目标大小: {target_width}x{target_height}")
            self.logger.info(f"📊 差异: 位置({position_x_diff}, {position_y_diff}), 大小({width_diff}, {height_diff})")

            if (width_diff <= tolerance and height_diff <= tolerance and
                position_x_diff <= tolerance and position_y_diff <= tolerance):
                self.logger.info("✅ 窗口大小和位置验证通过")
                return True
            else:
                self.logger.warning(f"⚠️ 窗口验证失败 - 位置差异: ({position_x_diff}, {position_y_diff}), 大小差异: ({width_diff}, {height_diff})")
                return False

        except Exception as e:
            self.logger.error(f"❌ 验证窗口大小和位置失败: {e}")
            return False

    def _is_main_wechat_window(self, hwnd: int, window_info: Dict) -> bool:
        """智能识别是否为微信主窗口"""
        try:
            class_name = window_info.get('class_name', '')
            title = window_info.get('title', '')
            rect = window_info.get('rect', (0, 0, 0, 0))

            # 计算窗口大小
            width = rect[2] - rect[0] if len(rect) >= 4 else 0
            height = rect[3] - rect[1] if len(rect) >= 4 else 0
            area = width * height

            self.logger.debug(f"窗口分析: 标题='{title}', 类名='{class_name}', 大小={width}x{height}, 面积={area}")

            # 检查窗口状态
            is_visible = win32gui.IsWindowVisible(hwnd)
            is_iconic = win32gui.IsIconic(hwnd)
            is_enabled = win32gui.IsWindowEnabled(hwnd)

            # 主窗口识别条件
            conditions = {
                'title_match': title == "微信" or title == "WeChat",
                'class_match': (class_name == "WeChatMainWndForPC" or
                              "Qt" in class_name and "QWindow" in class_name),
                'size_adequate': width >= 400 and height >= 300,  # 主窗口应该足够大
                'area_adequate': area >= 120000,  # 面积至少 400x300
                'window_visible': is_visible,
                'not_minimized': not is_iconic,
                'window_enabled': is_enabled,
                'not_tiny': area > 50000  # 排除小的托盘窗口
            }

            self.logger.debug(f"主窗口条件检查: {conditions}")

            # 如果是传统的 WeChatMainWndForPC 类名，直接认为是主窗口
            if class_name == "WeChatMainWndForPC":
                self.logger.info("✅ 发现传统微信主窗口类名")
                return True

            # 对于新版微信（Qt界面），使用综合判断
            if (conditions['title_match'] and
                conditions['class_match'] and
                conditions['size_adequate'] and
                conditions['window_visible'] and
                conditions['not_minimized'] and
                conditions['window_enabled'] and
                conditions['not_tiny']):

                self.logger.info(f"✅ 智能识别为微信主窗口: {width}x{height}, 面积={area}")
                return True

            # 如果窗口很大但可能被最小化，也尝试识别
            if (conditions['title_match'] and
                conditions['class_match'] and
                conditions['area_adequate'] and
                conditions['window_visible'] and
                conditions['window_enabled']):

                self.logger.info(f"✅ 识别为可能的微信主窗口（可能被最小化）: {width}x{height}")
                return True

            # 特殊处理：如果是微信窗口但被最小化到很小，尝试恢复后再判断
            if (conditions['title_match'] and
                conditions['class_match'] and
                conditions['window_visible'] and
                conditions['window_enabled'] and
                is_iconic):

                self.logger.info(f"🔄 发现被最小化的微信窗口，尝试恢复: {width}x{height}")

                try:
                    # 尝试恢复窗口
                    win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                    time.sleep(0.5)

                    # 重新获取窗口大小
                    new_rect = win32gui.GetWindowRect(hwnd)
                    new_width = new_rect[2] - new_rect[0]
                    new_height = new_rect[3] - new_rect[1]

                    self.logger.info(f"恢复后大小: {new_width}x{new_height}")

                    # 如果恢复后窗口足够大，认为是主窗口
                    if new_width >= 400 and new_height >= 300:
                        self.logger.info("✅ 恢复后确认为微信主窗口")
                        return True

                except Exception as e:
                    self.logger.debug(f"恢复窗口失败: {e}")
                    pass

            self.logger.debug("❌ 不符合主窗口条件")
            return False

        except Exception as e:
            self.logger.error(f"❌ 识别主窗口失败: {e}")
            return False

    def _move_window_to_top_left(self, hwnd: int) -> bool:
        """将窗口移动到配置指定的位置（使用成功的验证机制）"""
        try:
            # 从配置获取目标位置
            target_position = self.config.get("window_position", [0, 0])
            target_x, target_y = target_position[0], target_position[1]

            # 获取窗口当前位置和大小
            rect = win32gui.GetWindowRect(hwnd)
            current_left, current_top, current_right, current_bottom = rect

            # 计算窗口的宽度和高度
            window_width = current_right - current_left
            window_height = current_bottom - current_top

            self.logger.info(f"📏 当前窗口位置: ({current_left}, {current_top}), 大小: {window_width}x{window_height}")
            self.logger.info(f"🎯 目标位置: ({target_x}, {target_y})")

            # 检查窗口是否已经在目标位置
            if abs(current_left - target_x) <= 10 and abs(current_top - target_y) <= 10:
                self.logger.info(f"✅ 窗口已经在目标位置 ({target_x}, {target_y})，无需移动")
                return True

            # 尝试移动窗口（即使API可能报告失败）
            try:
                win32gui.MoveWindow(hwnd, target_x, target_y, window_width, window_height, True)
                time.sleep(0.2)
            except Exception as e:
                self.logger.debug(f"MoveWindow API 异常: {e}")

            # 验证移动结果（这是成功的关键）
            final_rect = win32gui.GetWindowRect(hwnd)
            final_left, final_top = final_rect[0], final_rect[1]

            if abs(final_left - target_x) <= 10 and abs(final_top - target_y) <= 10:
                self.logger.info(f"✅ 窗口成功移动到位置: ({final_left}, {final_top})")
                return True
            else:
                self.logger.warning(f"⚠️ 窗口移动后位置: ({final_left}, {final_top})，目标位置: ({target_x}, {target_y})")
                return False

        except Exception as e:
            self.logger.error(f"❌ 移动窗口失败: {e}")
            return False

    def _is_main_wechat_window_by_class_and_size(self, hwnd: int, window_text: str, class_name: str) -> bool:
        """通过类名和窗口大小智能判断是否为微信主窗口（增强版：支持新版微信Qt界面）"""
        try:
            # 传统微信主窗口类名
            if class_name == "WeChatMainWndForPC":
                self.logger.info(f"✅ 识别为传统微信主窗口: {class_name}")
                return True

            # 新版微信Qt界面判断（增强版）
            qt_patterns = [
                "Qt51514QWindowIcon",  # 新版微信常见类名
                "Qt5QWindowIcon",      # 其他Qt版本
                "Qt6QWindowIcon",      # Qt6版本
                "QtQWindowIcon"        # 通用Qt模式
            ]

            # 检查是否为Qt界面
            is_qt_window = (class_name in qt_patterns or
                           ("Qt" in class_name and ("QWindow" in class_name or "Icon" in class_name)))

            if is_qt_window:
                self.logger.debug(f"检测到Qt界面窗口: {class_name}")

                # 检查窗口标题
                if window_text in ['微信', 'WeChat']:
                    # 检查窗口大小和状态
                    rect = win32gui.GetWindowRect(hwnd)
                    width = rect[2] - rect[0]
                    height = rect[3] - rect[1]
                    area = width * height

                    # 检查窗口状态
                    is_visible = win32gui.IsWindowVisible(hwnd)
                    is_iconic = win32gui.IsIconic(hwnd)

                    self.logger.debug(f"Qt窗口分析: 大小={width}x{height}, 面积={area}, 可见={is_visible}, 最小化={is_iconic}")

                    # 降低主窗口识别门槛，适应新版微信
                    size_criteria = (
                        width >= 300 and height >= 200 and  # 降低最小尺寸要求
                        area >= 60000  # 降低最小面积要求
                    )

                    state_criteria = is_visible and not is_iconic

                    if size_criteria and state_criteria:
                        self.logger.info(f"✅ 识别为新版微信Qt主窗口: {width}x{height}, 面积={area}")
                        return True
                    elif size_criteria:
                        self.logger.info(f"✅ 识别为可能的微信Qt主窗口（状态异常）: {width}x{height}")
                        return True
                    else:
                        self.logger.debug(f"Qt窗口不符合主窗口条件: 大小={width}x{height}, 面积={area}")

            return False

        except Exception as e:
            self.logger.debug(f"判断主窗口失败: {e}")
            return False

    def get_window_info(self, hwnd: int) -> Dict:
        """获取窗口详细信息"""
        try:
            return {
                'hwnd': hwnd,
                'title': win32gui.GetWindowText(hwnd),
                'class_name': win32gui.GetClassName(hwnd),
                'rect': win32gui.GetWindowRect(hwnd),
                'is_visible': win32gui.IsWindowVisible(hwnd),
                'is_enabled': win32gui.IsWindowEnabled(hwnd),
                'is_iconic': win32gui.IsIconic(hwnd)
            }
        except Exception as e:
            self.logger.error(f"❌ 获取窗口信息失败: {e}")
            return {}
    
    def find_window_by_title(self, title_keyword: str) -> Optional[int]:
        """根据标题关键词查找窗口"""
        for window in self.wechat_windows:
            if title_keyword in window['title']:
                return window['hwnd']
        return None

    def monitor_and_maintain_window_state(self, hwnd: int, duration: float = 30.0) -> bool:
        """持续监控并维护窗口状态，确保窗口始终在前台可见"""
        try:
            self.logger.info(f"🔍 开始监控窗口状态 {duration} 秒...")
            start_time = time.time()
            check_interval = 2.0  # 每2秒检查一次
            last_check = 0

            while time.time() - start_time < duration:
                current_time = time.time()

                # 按间隔检查
                if current_time - last_check >= check_interval:
                    last_check = current_time

                    # 检查窗口是否仍然存在
                    if not win32gui.IsWindow(hwnd):
                        self.logger.warning("⚠️ 窗口句柄已失效，停止监控")
                        return False

                    # 检查窗口状态
                    current_hwnd = win32gui.GetForegroundWindow()
                    is_visible = win32gui.IsWindowVisible(hwnd)
                    is_iconic = win32gui.IsIconic(hwnd)

                    # 如果窗口不在前台或被最小化，重新激活
                    if current_hwnd != hwnd or not is_visible or is_iconic:
                        self.logger.info(f"🔄 检测到窗口状态异常，重新激活...")
                        self.logger.debug(f"当前前台窗口: {current_hwnd}, 目标窗口: {hwnd}")
                        self.logger.debug(f"可见: {is_visible}, 最小化: {is_iconic}")

                        # 重新激活窗口
                        if self._enhanced_activate_window(hwnd):
                            self.logger.info("✅ 窗口状态已恢复")
                        else:
                            self.logger.warning("⚠️ 窗口状态恢复失败")
                    else:
                        self.logger.debug("✅ 窗口状态正常")

                # 短暂休眠
                time.sleep(0.5)

            self.logger.info("✅ 窗口状态监控完成")
            return True

        except Exception as e:
            self.logger.error(f"❌ 窗口状态监控失败: {e}")
            return False

    def ensure_window_always_visible(self, hwnd: int) -> bool:
        """确保窗口始终可见且在前台（简化版，仅使用成功的方法）"""
        try:
            self.logger.info("🎯 执行窗口强化设置...")

            # 使用成功的激活方法
            activation_success = self._enhanced_activate_window(hwnd)

            if not activation_success:
                self.logger.warning("⚠️ 窗口激活失败")
                return False

            # 最终验证
            time.sleep(0.5)
            current_hwnd = win32gui.GetForegroundWindow()
            is_visible = win32gui.IsWindowVisible(hwnd)
            is_iconic = win32gui.IsIconic(hwnd)

            success = bool(current_hwnd == hwnd and is_visible and not is_iconic)

            if success:
                self.logger.info("✅ 窗口强化设置完成，窗口已稳定在前台")
            else:
                self.logger.warning(f"⚠️ 窗口强化设置部分失败")
                self.logger.debug(f"前台窗口: {current_hwnd == hwnd}, 可见: {is_visible}, 非最小化: {not is_iconic}")

            return success

        except Exception as e:
            self.logger.error(f"❌ 窗口强化设置失败: {e}")
            return False
    
    def wait_for_window(self, title_keyword: str, timeout: int = 10) -> Optional[int]:
        """等待特定窗口出现"""
        self.logger.info(f"⏳ 等待窗口出现: {title_keyword} (超时: {timeout}秒)")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            self.find_all_wechat_windows()  # 重新搜索窗口
            hwnd = self.find_window_by_title(title_keyword)
            if hwnd:
                self.logger.info(f"✅ 找到目标窗口: {title_keyword}")
                return hwnd
            time.sleep(0.5)
        
        self.logger.warning(f"⏰ 等待窗口超时: {title_keyword}")
        return None
    
    def close_window(self, hwnd: int) -> bool:
        """关闭指定窗口"""
        try:
            win32gui.PostMessage(hwnd, win32con.WM_CLOSE, 0, 0)
            self.logger.info(f"✅ 已发送关闭命令到窗口: {hwnd}")
            return True
        except Exception as e:
            self.logger.error(f"❌ 关闭窗口失败: {e}")
            return False

    def resize_wechat_main_window_to_target_size(self) -> bool:
        """专门用于调整微信主窗口大小的方法（从配置读取目标大小）"""
        try:
            # 从配置获取目标窗口大小
            window_size = self.window_management.get("main_window_size", {"width": 723, "height": 650})
            target_width = window_size.get("width", 723)
            target_height = window_size.get("height", 650)

            self.logger.info(f"🎯 开始查找并调整微信主窗口大小为 {target_width}x{target_height} 像素...")

            # 查找所有微信窗口
            self.find_all_wechat_windows()

            # 查找微信主窗口
            main_window = None
            for window in self.wechat_windows:
                if window['class_name'] == "WeChatMainWndForPC":
                    main_window = window
                    break

            if not main_window:
                self.logger.error("❌ 未找到微信主窗口")
                return False

            hwnd = main_window['hwnd']
            self.logger.info(f"✅ 找到微信主窗口: {main_window['title']} (句柄: {hwnd})")

            # 调整窗口大小
            resize_success = self.resize_main_window(hwnd, target_width, target_height)

            if resize_success:
                self.logger.info("🎉 微信主窗口大小调整完成！")

                # 最终验证
                final_rect = win32gui.GetWindowRect(hwnd)
                left, top, right, bottom = final_rect
                actual_width = right - left
                actual_height = bottom - top

                self.logger.info(f"📊 最终窗口状态:")
                self.logger.info(f"   位置: ({left}, {top})")
                self.logger.info(f"   大小: {actual_width}x{actual_height} 像素")
                self.logger.info(f"   目标: {target_width}x{target_height} 像素")

                # 检查是否在可接受范围内
                width_diff = abs(actual_width - target_width)
                height_diff = abs(actual_height - target_height)

                if width_diff <= 10 and height_diff <= 10:
                    self.logger.info("✅ 窗口大小调整验证通过")
                    return True
                else:
                    self.logger.warning(f"⚠️ 窗口大小存在偏差: 宽度差异{width_diff}px, 高度差异{height_diff}px")
                    return False
            else:
                self.logger.error("❌ 微信主窗口大小调整失败")
                return False

        except Exception as e:
            self.logger.error(f"❌ 调整微信主窗口大小过程中发生错误: {e}")
            return False
    
    def get_window_count(self) -> int:
        """获取微信窗口数量"""
        return len(self.wechat_windows)
    
    def refresh_windows(self) -> List[Dict]:
        """刷新窗口列表"""
        self.logger.info("🔄 刷新微信窗口列表...")
        return self.find_all_wechat_windows()
    
    def get_window_status_report(self) -> str:
        """获取窗口状态报告"""
        if not self.wechat_windows:
            return "❌ 未找到任何微信窗口"

        report = f"📊 微信窗口状态报告 (共{len(self.wechat_windows)}个窗口):\n"
        for i, window in enumerate(self.wechat_windows):
            status = "🟢 当前" if i == self.current_window_index else "⚪ 待用"
            window_name = f"微信{i+1}"
            report += f"  {status} 窗口{i+1}: {window_name} (句柄: {window['hwnd']})\n"

        return report

    def get_current_window_display_name(self) -> str:
        """获取当前窗口的显示名称"""
        if not self.wechat_windows or self.current_window_index >= len(self.wechat_windows):
            return "微信"

        return f"微信{self.current_window_index + 1}"

    def _is_add_friend_window(self, hwnd: int, window_info: Dict) -> bool:
        """检查指定窗口是否为添加朋友窗口"""
        try:
            title = window_info.get('title', '')

            # 检查窗口标题是否匹配添加朋友窗口
            if title in ['添加朋友', 'Add Friends']:
                self.logger.debug(f"✅ 识别为添加朋友窗口: {title} (句柄: {hwnd})")
                return True

            return False

        except Exception as e:
            self.logger.error(f"❌ 检查添加朋友窗口失败: {e}")
            return False

    def wait_for_add_friend_window(self, timeout: int = 10) -> Optional[int]:
        """等待添加朋友窗口出现"""
        try:
            self.logger.info(f"⏳ 等待添加朋友窗口出现（超时: {timeout}秒）...")

            start_time = time.time()
            while time.time() - start_time < timeout:
                # 重新搜索窗口
                self.find_all_wechat_windows()

                # 查找添加朋友窗口
                for window in self.wechat_windows:
                    if window['title'] in ['添加朋友', 'Add Friends']:
                        hwnd = window['hwnd']
                        self.logger.info(f"✅ 找到添加朋友窗口: {window['title']} (句柄: {hwnd})")

                        # 激活窗口
                        if self.activate_window(hwnd):
                            self.logger.info("✅ 添加朋友窗口激活成功")
                            return hwnd
                        else:
                            self.logger.warning("⚠️ 添加朋友窗口激活失败")

                # 等待0.5秒后重试
                time.sleep(0.5)

            self.logger.warning(f"⚠️ 在{timeout}秒内未找到添加朋友窗口")
            return None

        except Exception as e:
            self.logger.error(f"❌ 等待添加朋友窗口失败: {e}")
            return None

    def set_window_topmost(self, hwnd: int, topmost: bool = True) -> bool:
        """设置窗口置顶状态"""
        try:
            import win32con

            if topmost:
                # 设置窗口置顶
                result = win32gui.SetWindowPos(
                    hwnd,
                    win32con.HWND_TOPMOST,
                    0, 0, 0, 0,
                    win32con.SWP_NOMOVE | win32con.SWP_NOSIZE
                )
                self.logger.info(f"✅ 窗口已设置为置顶: {hwnd} (API返回: {result})")
            else:
                # 取消窗口置顶
                result = win32gui.SetWindowPos(
                    hwnd,
                    win32con.HWND_NOTOPMOST,
                    0, 0, 0, 0,
                    win32con.SWP_NOMOVE | win32con.SWP_NOSIZE
                )
                self.logger.info(f"✅ 窗口置顶状态已取消: {hwnd} (API返回: {result})")

            # 注意：SetWindowPos有时返回0但实际成功，所以我们总是返回True
            # 除非发生异常，否则认为操作成功
            return True

        except Exception as e:
            self.logger.error(f"❌ 设置窗口置顶状态失败: {e}")
            return False

    def ensure_window_on_top(self, hwnd: int) -> bool:
        """确保窗口在最前面（激活+置顶）"""
        try:
            self.logger.info(f"🔝 确保窗口在最前面: {hwnd}")

            # 1. 先激活窗口
            if not self.activate_window(hwnd):
                self.logger.warning("⚠️ 窗口激活失败，但继续尝试置顶")

            # 2. 设置窗口置顶
            if not self.set_window_topmost(hwnd, True):
                self.logger.warning("⚠️ 窗口置顶设置失败")
                return False

            # 3. 短暂延迟确保设置生效
            time.sleep(0.3)

            # 4. 验证窗口是否在前台
            current_hwnd = win32gui.GetForegroundWindow()
            if current_hwnd == hwnd:
                self.logger.info("✅ 窗口成功置于最前面")
                return True
            else:
                self.logger.warning(f"⚠️ 窗口可能未完全置于最前面 (当前前台: {current_hwnd})")
                return True  # 仍然返回True，因为置顶已设置

        except Exception as e:
            self.logger.error(f"❌ 确保窗口在最前面失败: {e}")
            return False

    def remove_all_topmost_status(self) -> bool:
        """移除所有微信窗口的置顶状态"""
        try:
            self.logger.info("🔄 移除所有微信窗口的置顶状态...")

            success_count = 0
            for window in self.wechat_windows:
                hwnd = window.get('hwnd')
                if hwnd:
                    if self.set_window_topmost(hwnd, False):
                        success_count += 1

            self.logger.info(f"✅ 成功移除 {success_count} 个窗口的置顶状态")
            return success_count > 0

        except Exception as e:
            self.logger.error(f"❌ 移除置顶状态失败: {e}")
            return False

    def debug_all_windows(self) -> None:
        """调试功能：显示所有可见窗口及其进程信息"""
        try:
            self.logger.info("🔍 开始调试所有可见窗口...")

            def debug_enum_callback(hwnd, debug_info):
                if win32gui.IsWindowVisible(hwnd):
                    try:
                        window_text = win32gui.GetWindowText(hwnd)
                        class_name = win32gui.GetClassName(hwnd)
                        process_name = self._get_process_name_by_hwnd(hwnd)

                        if window_text:  # 只显示有标题的窗口
                            is_wechat_process = self._is_wechat_process(process_name) if process_name else False
                            status = "✅ 微信进程" if is_wechat_process else "❌ 非微信进程"

                            debug_info.append({
                                'hwnd': hwnd,
                                'title': window_text,
                                'class_name': class_name,
                                'process_name': process_name,
                                'is_wechat_process': is_wechat_process
                            })

                            self.logger.info(f"  {status} | 标题: '{window_text}' | 类名: '{class_name}' | 进程: {process_name} | 句柄: {hwnd}")
                    except Exception as e:
                        self.logger.debug(f"获取窗口 {hwnd} 信息失败: {e}")
                return True

            debug_windows = []
            win32gui.EnumWindows(debug_enum_callback, debug_windows)

            # 统计信息
            total_windows = len(debug_windows)
            wechat_process_windows = sum(1 for w in debug_windows if w['is_wechat_process'])

            self.logger.info(f"📊 调试统计: 总窗口数={total_windows}, 微信进程窗口数={wechat_process_windows}")

        except Exception as e:
            self.logger.error(f"❌ 调试所有窗口失败: {e}")

    def cleanup(self) -> None:
        """清理资源"""
        try:
            self.logger.info("🧹 清理窗口管理器资源...")

            # 清理窗口置顶状态
            self.remove_all_topmost_status()

            # 清理窗口列表
            self.wechat_windows.clear()
            self.current_window_index = 0

            self.logger.info("✅ 窗口管理器资源清理完成")

        except Exception as e:
            self.logger.error(f"❌ 清理窗口管理器资源失败: {e}")

def main():
    """智能测试函数 - 展示改进的窗口激活功能"""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    print("🚀 微信窗口管理器 - 智能激活版本")
    print("=" * 50)

    manager = WeChatWindowManager()

    # 查找所有微信窗口
    print("🔍 正在搜索微信窗口...")
    windows = manager.find_all_wechat_windows()
    print(manager.get_window_status_report())

    if windows:
        print(f"\n✅ 发现 {len(windows)} 个微信窗口")
        print("🔄 开始智能窗口切换测试...")
        print("📝 注意：新版本使用智能激活策略，减少了激活失败的警告")
        print("-" * 50)

        # 测试窗口切换
        for i in range(min(3, len(windows))):
            print(f"\n🎯 切换测试 {i+1}/{min(3, len(windows))}")

            current = manager.switch_to_next_window()
            if current:
                current_display_name = manager.get_current_window_display_name()
                print(f"✅ 当前窗口: {current_display_name} (句柄: {current['hwnd']})")

                # 验证窗口状态
                is_visible = win32gui.IsWindowVisible(current['hwnd'])
                is_iconic = win32gui.IsIconic(current['hwnd'])
                is_enabled = win32gui.IsWindowEnabled(current['hwnd'])

                status_info = []
                if is_visible:
                    status_info.append("✅ 可见")
                else:
                    status_info.append("❌ 不可见")

                if not is_iconic:
                    status_info.append("✅ 非最小化")
                else:
                    status_info.append("❌ 最小化")

                if is_enabled:
                    status_info.append("✅ 启用")
                else:
                    status_info.append("❌ 禁用")

                print(f"📊 窗口状态: {' | '.join(status_info)}")
            else:
                print("⚠️ 窗口切换遇到问题，但程序继续运行")

            time.sleep(2)

        print("\n" + "=" * 50)
        print("✅ 智能窗口切换测试完成")
        print("💡 提示：即使看到'激活失败'信息，窗口切换通常仍然有效")
        print("💡 新版本使用多重激活策略，提高了成功率")

    else:
        print("❌ 未找到任何微信窗口")
        print("\n📋 请检查以下条件:")
        print("1. ✅ 微信已经启动")
        print("2. ✅ 微信窗口可见（未最小化到托盘）")
        print("3. ✅ 微信窗口标题为'微信'或'WeChat'")
        print("4. ✅ 微信进程名为'Weixin.exe'或'WeChat.exe'")

        # 调试信息：显示所有可见窗口及其进程信息
        print("\n🔍 系统调试信息:")
        print("-" * 30)
        manager.debug_all_windows()

if __name__ == "__main__":
    main()
