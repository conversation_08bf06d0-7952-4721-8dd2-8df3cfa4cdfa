#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信自动化添加好友 - GUI启动脚本
简化的启动入口，包含环境检查和错误处理

版本：1.0.0
作者：AI助手
创建时间：2025-01-28
"""

import sys
import os
import traceback
from pathlib import Path


def check_environment():
    """检查运行环境"""
    print("🔍 正在检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 错误: 需要Python 3.7或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查必要的模块
    required_modules = [
        'tkinter',
        'threading',
        'queue',
        'json',
        'logging',
        'pathlib'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ 模块 {module}: 已安装")
        except ImportError:
            missing_modules.append(module)
            print(f"❌ 模块 {module}: 未安装")
    
    if missing_modules:
        print(f"❌ 缺少必要模块: {', '.join(missing_modules)}")
        return False
    
    # 检查项目文件
    required_files = [
        'main_controller.py',
        'config.json',
        'wechat_automation_gui.py'
    ]
    
    missing_files = []
    for file in required_files:
        if Path(file).exists():
            print(f"✅ 文件 {file}: 存在")
        else:
            missing_files.append(file)
            print(f"❌ 文件 {file}: 不存在")
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    # 检查modules目录
    modules_dir = Path("modules")
    if modules_dir.exists() and modules_dir.is_dir():
        print("✅ modules目录: 存在")
        
        # 检查关键模块文件
        key_modules = [
            'window_manager.py',
            'data_manager.py',
            'main_interface.py'
        ]
        
        for module in key_modules:
            module_path = modules_dir / module
            if module_path.exists():
                print(f"✅ 模块 {module}: 存在")
            else:
                print(f"⚠️ 模块 {module}: 不存在（可能影响功能）")
    else:
        print("❌ modules目录: 不存在")
        return False
    
    print("✅ 环境检查完成")
    return True


def check_excel_file():
    """检查Excel文件"""
    excel_file = "添加好友名单.xlsx"
    if Path(excel_file).exists():
        print(f"✅ Excel文件: {excel_file} 存在")
        return True
    else:
        print(f"⚠️ Excel文件: {excel_file} 不存在")
        print("💡 提示: 您可以在GUI中选择其他Excel文件")
        return True  # 不阻止启动，用户可以在GUI中选择


def create_logs_directory():
    """创建日志目录"""
    try:
        logs_dir = Path("logs")
        logs_dir.mkdir(exist_ok=True)
        
        subdirs = ["current", "archive", "temp", "gui"]
        for subdir in subdirs:
            (logs_dir / subdir).mkdir(exist_ok=True)
        
        print("✅ 日志目录: 已创建")
        return True
    except Exception as e:
        print(f"⚠️ 创建日志目录失败: {e}")
        return True  # 不阻止启动


def main():
    """主程序"""
    print("=" * 60)
    print("🚀 微信自动化添加好友 - GUI启动器")
    print("=" * 60)
    
    try:
        # 环境检查
        if not check_environment():
            print("\n❌ 环境检查失败，无法启动程序")
            input("按回车键退出...")
            return
        
        # 检查Excel文件
        check_excel_file()
        
        # 创建日志目录
        create_logs_directory()
        
        print("\n🎉 环境检查通过，正在启动GUI...")
        print("-" * 60)
        
        # 导入并启动GUI
        try:
            from wechat_automation_gui import WeChatAutomationGUI
            
            print("✅ GUI模块加载成功")
            print("🖥️ 正在启动图形界面...")
            
            # 创建并运行GUI
            app = WeChatAutomationGUI()
            app.run()
            
        except ImportError as e:
            print(f"❌ 导入GUI模块失败: {e}")
            print("💡 请确保 wechat_automation_gui.py 文件存在且格式正确")
            
        except Exception as e:
            print(f"❌ 启动GUI失败: {e}")
            print("\n📋 详细错误信息:")
            traceback.print_exc()
    
    except KeyboardInterrupt:
        print("\n\n⏹️ 用户中断程序")
    
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        print("\n📋 详细错误信息:")
        traceback.print_exc()
    
    finally:
        print("\n" + "=" * 60)
        print("👋 程序已退出")
        print("=" * 60)


if __name__ == "__main__":
    main()
