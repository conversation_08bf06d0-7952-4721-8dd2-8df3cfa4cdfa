# 微信自动化程序 - 动态参数响应功能说明

## 功能概述

微信自动化程序现已完全支持动态参数响应功能，能够实时读取并应用用户在GUI界面中修改的数量限制参数，确保程序严格按照用户设置执行。

## 核心功能特性

### 1. 动态参数响应
- **实时读取**：程序能够实时读取GUI界面中修改的参数设置
- **自动应用**：参数修改后立即生效，无需重启程序
- **缓存优化**：采用5秒缓存机制，平衡性能与实时性

### 2. 严格执行控制
- **单窗口限制**：当达到单窗口最大限制时，程序立即终止，不会切换到下一个微信窗口
- **每日限制**：当达到每日添加上限时，程序自动终止
- **强制终止**：所有限制都会触发程序完全停止，确保不超出用户设置

### 3. 实时参数同步
- **GUI界面**：用户在界面中修改参数时立即保存到配置文件
- **配置文件**：config.json实时更新，确保参数同步
- **模块间同步**：主控制器和自动添加模块都能实时读取最新参数

## 技术实现

### 核心组件

#### 1. WeChatAutoAddFriend模块
```python
def _get_realtime_runtime_params(self):
    """动态参数响应 - 实时读取GUI设置的运行参数"""
    # 5秒缓存机制
    # 实时读取config.json
    # 返回最新的运行参数
```

#### 2. WeChatMainController模块
```python
def _get_realtime_runtime_params(self):
    """主控制器动态参数响应"""
    # 与自动添加模块相同的实时读取机制
    # 确保主控制器也能获取最新参数
```

#### 3. GUI界面增强
```python
def on_param_change(self, _=None):
    """参数变化时的回调函数 - 增强版：动态参数响应"""
    # 实时验证和应用参数
    # 立即保存到配置文件
    # 如果控制器正在运行，立即应用新参数
```

### 关键修改点

#### 1. 实时参数检查
- 在联系人处理循环中，每次都调用`_get_realtime_runtime_params()`
- 确保使用最新的数量限制参数进行检查

#### 2. 强制终止机制
- 单窗口限制达到时返回`DAILY_LIMIT_REACHED`状态码
- 触发程序完全终止，而不是切换窗口

#### 3. 配置文件同步
- GUI参数变更时立即更新config.json
- 同步更新multi_window配置，确保一致性

## 使用说明

### 1. 设置数量限制
1. 在GUI界面中设置"每日添加上限"和"单窗口最大限制"
2. 参数会立即保存到配置文件
3. 正在运行的程序会在下次检查时应用新参数

### 2. 严格执行模式
- **单窗口限制为3人**：程序在当前窗口添加完第3个好友后立即停止
- **每日限制为10人**：程序在总共添加10个好友后自动终止
- **不会继续处理**：达到任何限制后，程序完全停止，不会切换窗口或继续执行

### 3. 实时监控
- 程序运行时会显示当前已处理数量和限制数量
- 日志中会记录实时参数更新信息
- 达到限制时会显示详细的完成信息

## 测试验证

### 测试结果
✅ **WeChatAutoAddFriend动态参数** - 测试通过  
✅ **WeChatMainController动态参数** - 测试通过  
✅ **缓存性能** - 测试通过，提速98.9%  

### 测试覆盖
- 实时参数读取功能
- 参数缓存机制
- 配置文件同步
- 数量限制强制执行
- 单窗口限制终止机制

## 配置文件格式

```json
{
  "runtime_parameters": {
    "daily_add_limit": {
      "value": 10,
      "description": "每日添加上限"
    },
    "max_adds_per_window": {
      "value": 3,
      "description": "每窗口最大添加数量"
    }
  },
  "multi_window": {
    "contacts_per_window": 3
  }
}
```

## 日志示例

```
🔄 实时参数更新：每日限制=10，单窗口限制=3
🔍 实时数量限制检查：当前已处理 2/10 个联系人
🎯 已处理 3 个联系人，达到单窗口最大限制 (3)
🛑 根据用户设置，程序必须立即终止，不得继续处理后续联系人
🛑 程序将完全停止，不会切换到下一个微信窗口
✅ 单窗口任务完成，程序强制终止
```

## 注意事项

1. **参数生效时间**：新参数在下次检查时生效（最多5秒延迟）
2. **程序终止**：达到限制后程序会完全停止，需要手动重启
3. **配置备份**：建议在修改重要参数前备份配置文件
4. **日志监控**：通过日志可以实时监控参数应用情况

## 版本信息

- **功能版本**：v2.0
- **实现日期**：2025-08-01
- **测试状态**：✅ 全部通过
- **兼容性**：向后兼容，不影响现有功能

---

**总结**：动态参数响应功能已完全实现并通过测试，用户现在可以在程序运行时实时调整数量限制参数，程序会严格按照设置执行，确保不超出用户指定的限制。
