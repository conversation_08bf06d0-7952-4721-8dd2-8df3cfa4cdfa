# 微信自动化添加好友程序依赖包 - 重构版
# 重构日期: 2025-07-17
# 说明: 已移除OCR和图像处理相关依赖，专注于纯UI自动化
#
# 安装方法：
# 1. 使用清华镜像源（推荐，国内用户）：
#    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
#
# 2. 使用官方源：
#    pip install -r requirements.txt
#
# 3. 使用其他镜像源：
#    阿里云：pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/
#    豆瓣：  pip install -r requirements.txt -i https://pypi.douban.com/simple/

# 核心依赖
Pillow>=9.0.0
numpy>=1.21.0
requests>=2.25.0

# 数据处理
pandas>=1.5.0
openpyxl>=3.0.0

# UI自动化
pyautogui>=0.9.54

# 系统和进程管理
pywin32>=304

# OCR识别（用于截图文字识别）
pytesseract>=0.3.10

# 网络和HTTP
urllib3>=1.26.0

# 开发和测试工具（可选）
# pytest>=7.0.0
# pytest-cov>=4.0.0

# 已移除的依赖项（重构后不再需要）：
# opencv-python>=4.5.0    # 图像处理
# Pillow>=9.0.0          # 图像处理
# numpy>=1.21.0          # 数值计算
# paddleocr>=2.7.0       # OCR识别
# easyocr>=1.7.0         # OCR识别
# pytesseract>=0.3.10    # OCR识别
