#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置和工具模块
功能：配置管理、坐标定义、工具函数等
"""

import json
import time
import random
import logging
import logging.handlers
import os
import shutil
import glob
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional
import numpy as np

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str = "config.json"):
        self.config_path = config_path
        self.config = self._load_config()
        self.logger = logging.getLogger(__name__)
    
    def _load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            "excel_file": "添加好友名单.xlsx",
            "log_file": "logs/wechat_auto.log",
            "delay_range": [1.5, 3.0],
            "click_delay": [0.3, 0.8],
            "retry_times": 3,
            "batch_size": 50,
            "optimized_coordinates": {
                "微信按钮": [31, 95],
                "通讯录按钮": [29, 144],
                "微信主按钮": [31, 95],
                "+快捷操作按钮": [244, 41],
                "添加朋友选项": [252, 125],
                "搜索输入框": [923, 389],
                "搜索按钮": [1064, 390],

            },
            "mouse_optimization": {
                "slow": {"duration": 0.5, "pause": 0.2, "click_delay": 0.3},
                "medium": {"duration": 0.3, "pause": 0.1, "click_delay": 0.2},
                "fast": {"duration": 0.1, "pause": 0.05, "click_delay": 0.1}
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        return self.config.get(key, default)
    
    def set(self, key: str, value: Any) -> bool:
        """设置配置值"""
        try:
            self.config[key] = value
            return self.save()
        except Exception as e:
            self.logger.error(f"❌ 设置配置失败: {e}")
            return False
    
    def save(self) -> bool:
        """保存配置到文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            self.logger.error(f"❌ 保存配置失败: {e}")
            return False

class CoordinateManager:
    """坐标管理器"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.coordinates = config_manager.get("optimized_coordinates", {})
        self.logger = logging.getLogger(__name__)
    
    def get_coordinate(self, element_name: str) -> Optional[Tuple[int, int]]:
        """获取元素坐标"""
        coord = self.coordinates.get(element_name)
        if coord and len(coord) == 2:
            return tuple(coord)
        self.logger.error(f"❌ 未找到元素坐标: {element_name}")
        return None
    
    def set_coordinate(self, element_name: str, x: int, y: int) -> bool:
        """设置元素坐标"""
        try:
            self.coordinates[element_name] = [x, y]
            self.config_manager.set("optimized_coordinates", self.coordinates)
            self.logger.info(f"✅ 更新坐标 {element_name}: ({x}, {y})")
            return True
        except Exception as e:
            self.logger.error(f"❌ 设置坐标失败: {e}")
            return False
    
    def get_all_coordinates(self) -> Dict[str, List[int]]:
        """获取所有坐标"""
        return self.coordinates.copy()
    
    def validate_coordinates(self) -> Dict[str, bool]:
        """验证坐标有效性"""
        required_elements = [
            "微信按钮", "通讯录按钮", "微信主按钮", "+快捷操作按钮",
            "添加朋友选项", "搜索输入框", "搜索按钮"
        ]
        
        validation_result = {}
        for element in required_elements:
            coord = self.get_coordinate(element)
            validation_result[element] = coord is not None
        
        return validation_result

class DelayManager:
    """延迟管理器"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.delay_range = config_manager.get("delay_range", [1.5, 3.0])
        self.click_delay = config_manager.get("click_delay", [0.3, 0.8])
        self.logger = logging.getLogger(__name__)
    
    def get_random_delay(self, delay_type: str = "normal") -> float:
        """获取随机延迟时间"""
        if delay_type == "click":
            return random.uniform(self.click_delay[0], self.click_delay[1])
        elif delay_type == "normal":
            return random.uniform(self.delay_range[0], self.delay_range[1])
        elif delay_type == "short":
            return random.uniform(0.5, 1.0)
        elif delay_type == "long":
            return random.uniform(3.0, 5.0)
        else:
            return random.uniform(1.0, 2.0)
    
    def smart_delay(self, base_delay: float = 1.0, variance: float = 0.3) -> float:
        """智能延迟，模拟人类操作"""
        # 添加随机变化
        delay = base_delay + random.uniform(-variance, variance)
        
        # 确保最小延迟
        delay = max(delay, 0.1)
        
        return delay
    
    def wait(self, delay_type: str = "normal") -> None:
        """等待指定类型的延迟"""
        delay = self.get_random_delay(delay_type)
        self.logger.debug(f"⏳ 等待 {delay:.2f} 秒 ({delay_type})")
        time.sleep(delay)

class SafetyManager:
    """安全管理器"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.safety_config = config_manager.get("safety", {})
        self.max_operations_per_hour = self.safety_config.get("max_operations_per_hour", 100)
        self.cool_down_time = self.safety_config.get("cool_down_time", 300)
        self.operation_history = []
        self.logger = logging.getLogger(__name__)
    
    def check_operation_limit(self) -> bool:
        """检查操作频率限制"""
        current_time = time.time()
        
        # 清理1小时前的记录
        self.operation_history = [
            op_time for op_time in self.operation_history
            if current_time - op_time < 3600
        ]
        
        # 检查是否超过限制
        if len(self.operation_history) >= self.max_operations_per_hour:
            self.logger.warning(f"⚠️ 操作频率超限，当前1小时内操作次数: {len(self.operation_history)}")
            return False
        
        return True
    
    def record_operation(self) -> None:
        """记录操作"""
        self.operation_history.append(time.time())
    
    def should_cool_down(self) -> bool:
        """判断是否需要冷却"""
        if len(self.operation_history) < 2:
            return False
        
        # 检查最近两次操作的间隔
        last_two_ops = sorted(self.operation_history[-2:])
        interval = last_two_ops[1] - last_two_ops[0]
        
        return interval < self.cool_down_time
    
    def get_cool_down_time(self) -> float:
        """获取建议的冷却时间"""
        if not self.should_cool_down():
            return 0
        
        last_op_time = max(self.operation_history)
        elapsed = time.time() - last_op_time
        remaining = self.cool_down_time - elapsed
        
        return max(remaining, 0)

class UtilityFunctions:
    """工具函数集合"""
    
    @staticmethod
    def format_phone_number(phone: str) -> str:
        """格式化手机号"""
        # 移除所有非数字字符
        phone = ''.join(filter(str.isdigit, phone))
        
        # 检查长度
        if len(phone) == 11 and phone.startswith('1'):
            return phone
        elif len(phone) == 13 and phone.startswith('86'):
            return phone[2:]  # 移除国家代码
        else:
            return phone
    
    @staticmethod
    def validate_phone_number(phone: str) -> bool:
        """验证手机号格式"""
        phone = UtilityFunctions.format_phone_number(phone)
        
        # 中国手机号验证
        if len(phone) != 11:
            return False
        
        if not phone.startswith('1'):
            return False
        
        # 第二位数字验证
        valid_second_digits = ['3', '4', '5', '6', '7', '8', '9']
        if phone[1] not in valid_second_digits:
            return False
        
        return True
    
    @staticmethod
    def generate_verification_message(template: Optional[str] = None) -> str:
        """生成验证信息"""
        if template:
            return template
        
        messages = [
            "你好，我想加你为好友",
            "您好，希望能够认识您",
            "你好，可以加个好友吗",
            "您好，想和您交个朋友",
            "你好，通过朋友介绍认识的"
        ]
        
        return random.choice(messages)
    
    @staticmethod
    def create_directories(paths: List[str]) -> None:
        """创建目录"""
        for path in paths:
            os.makedirs(path, exist_ok=True)
    
    @staticmethod
    def get_timestamp(format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
        """获取时间戳（修复：使用正确的北京时区）"""
        from datetime import datetime, timezone, timedelta
        beijing_tz = timezone(timedelta(hours=8))
        return datetime.now(beijing_tz).strftime(format_str)
    
    @staticmethod
    def calculate_success_rate(total: int, success: int) -> float:
        """计算成功率"""
        if total == 0:
            return 0.0
        return (success / total) * 100
    
    @staticmethod
    def estimate_remaining_time(processed: int, total: int, start_time: float) -> str:
        """估算剩余时间"""
        if processed == 0:
            return "未知"
        
        elapsed = time.time() - start_time
        avg_time_per_item = elapsed / processed
        remaining_items = total - processed
        remaining_seconds = remaining_items * avg_time_per_item
        
        hours = int(remaining_seconds // 3600)
        minutes = int((remaining_seconds % 3600) // 60)
        seconds = int(remaining_seconds % 60)
        
        if hours > 0:
            return f"{hours}小时{minutes}分钟"
        elif minutes > 0:
            return f"{minutes}分钟{seconds}秒"
        else:
            return f"{seconds}秒"

class LogManager:
    """增强的日志管理器"""

    def __init__(self, config_manager: 'ConfigManager'):
        self.config_manager = config_manager
        self.logging_config = config_manager.get('logging', {})
        self.logger = logging.getLogger(__name__)

    def setup_logging(self, log_file: Optional[str] = None, level: Optional[int] = None) -> None:
        """设置增强的日志系统"""
        # 获取配置
        if log_file is None:
            log_file = self.config_manager.get('log_file', 'logs/current/wechat_auto.log')
        if level is None:
            level_str = self.logging_config.get('level', 'INFO')
            level = getattr(logging, level_str.upper(), logging.INFO)

        # 类型断言：此时 log_file 和 level 都不会是 None
        assert log_file is not None
        assert level is not None

        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        os.makedirs(log_dir, exist_ok=True)

        # 确保归档和临时目录存在
        archive_path = self.logging_config.get('archive_path', 'logs/archive')
        temp_path = self.logging_config.get('temp_path', 'logs/temp')
        os.makedirs(archive_path, exist_ok=True)
        os.makedirs(temp_path, exist_ok=True)

        # 配置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # 清除现有处理器
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # 轮转文件处理器
        max_bytes = self._parse_size(self.logging_config.get('max_file_size', '10MB'))
        backup_count = self.logging_config.get('backup_count', 5)

        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)

        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)

        # 配置根日志器
        root_logger.setLevel(level)
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)

        self.logger.info(f"✅ 日志系统初始化完成: {log_file}")

        # 启动自动清理（如果启用）
        if self.logging_config.get('auto_cleanup', True):
            self.schedule_cleanup()

    def _parse_size(self, size_str: str) -> int:
        """解析文件大小字符串"""
        size_str = size_str.upper()
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)

    def schedule_cleanup(self) -> None:
        """安排日志清理任务"""
        try:
            import threading
            import schedule

            cleanup_time = self.logging_config.get('cleanup_time', '02:00')

            def run_cleanup():
                schedule.every().day.at(cleanup_time).do(self.cleanup_old_logs)
                while True:
                    schedule.run_pending()
                    time.sleep(60)  # 每分钟检查一次

            # 在后台线程中运行清理调度
            cleanup_thread = threading.Thread(target=run_cleanup, daemon=True)
            cleanup_thread.start()

            self.logger.info(f"✅ 日志自动清理已启动，每日 {cleanup_time} 执行")

        except ImportError:
            self.logger.warning("⚠️ schedule模块未安装，无法启用自动清理")
        except Exception as e:
            self.logger.error(f"❌ 启动自动清理失败: {e}")

    def cleanup_old_logs(self) -> Dict[str, int]:
        """清理旧日志文件"""
        self.logger.info("🧹 开始清理旧日志文件...")

        retention_days = self.logging_config.get('retention_days', 30)
        cutoff_date = datetime.now() - timedelta(days=retention_days)

        stats = {
            'deleted_files': 0,
            'deleted_size': 0,
            'archived_files': 0,
            'compressed_files': 0
        }

        try:
            # 清理归档目录中的旧文件
            archive_path = self.logging_config.get('archive_path', 'logs/archive')
            if os.path.exists(archive_path):
                stats.update(self._cleanup_directory(archive_path, cutoff_date))

            # 清理临时目录（保留7天）
            temp_path = self.logging_config.get('temp_path', 'logs/temp')
            temp_cutoff = datetime.now() - timedelta(days=7)
            if os.path.exists(temp_path):
                temp_stats = self._cleanup_directory(temp_path, temp_cutoff)
                stats['deleted_files'] += temp_stats['deleted_files']
                stats['deleted_size'] += temp_stats['deleted_size']

            # 压缩大文件
            self._compress_large_files()

            self.logger.info(f"""
🎯 日志清理完成:
   📁 删除文件: {stats['deleted_files']} 个
   💾 释放空间: {stats['deleted_size'] / 1024 / 1024:.1f} MB
   📦 归档文件: {stats['archived_files']} 个
   🗜️ 压缩文件: {stats['compressed_files']} 个
            """)

        except Exception as e:
            self.logger.error(f"❌ 日志清理失败: {e}")

        return stats

    def _cleanup_directory(self, directory: str, cutoff_date: datetime) -> Dict[str, int]:
        """清理指定目录中的旧文件"""
        stats = {'deleted_files': 0, 'deleted_size': 0, 'archived_files': 0}

        for root, dirs, files in os.walk(directory):
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    if file_time < cutoff_date:
                        file_size = os.path.getsize(file_path)
                        os.remove(file_path)
                        stats['deleted_files'] += 1
                        stats['deleted_size'] += file_size
                        self.logger.debug(f"🗑️ 删除旧日志: {file_path}")
                except Exception as e:
                    self.logger.warning(f"⚠️ 删除文件失败 {file_path}: {e}")

        return stats

    def _compress_large_files(self) -> None:
        """压缩大型日志文件"""
        try:
            import gzip

            archive_path = self.logging_config.get('archive_path', 'logs/archive')
            max_size = self._parse_size('10MB')  # 超过10MB的文件进行压缩

            for root, dirs, files in os.walk(archive_path):
                for file in files:
                    if file.endswith('.log') and not file.endswith('.gz'):
                        file_path = os.path.join(root, file)
                        if os.path.getsize(file_path) > max_size:
                            self._compress_file(file_path)

        except ImportError:
            self.logger.warning("⚠️ gzip模块不可用，跳过文件压缩")
        except Exception as e:
            self.logger.error(f"❌ 压缩文件失败: {e}")

    def _compress_file(self, file_path: str) -> None:
        """压缩单个文件"""
        try:
            import gzip

            compressed_path = file_path + '.gz'
            with open(file_path, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)

            os.remove(file_path)
            self.logger.info(f"🗜️ 压缩完成: {file_path} -> {compressed_path}")

        except Exception as e:
            self.logger.error(f"❌ 压缩文件失败 {file_path}: {e}")

    def archive_current_logs(self) -> None:
        """归档当前日志文件"""
        try:
            current_path = self.logging_config.get('current_path', 'logs/current')
            archive_path = self.logging_config.get('archive_path', 'logs/archive')

            if not os.path.exists(current_path):
                return

            # 创建今日归档目录
            today = datetime.now()
            archive_dir = os.path.join(
                archive_path,
                str(today.year),
                f"{today.month:02d}",
                f"{today.day:02d}"
            )
            os.makedirs(archive_dir, exist_ok=True)

            # 移动当前日志文件
            for file in os.listdir(current_path):
                if file.endswith('.log'):
                    src_path = os.path.join(current_path, file)
                    timestamp = today.strftime('%H%M%S')
                    archived_name = f"{file[:-4]}_{today.strftime('%Y%m%d')}_{timestamp}.log"
                    dst_path = os.path.join(archive_dir, archived_name)

                    shutil.move(src_path, dst_path)
                    self.logger.info(f"📦 归档日志: {src_path} -> {dst_path}")

        except Exception as e:
            self.logger.error(f"❌ 归档日志失败: {e}")


def setup_logging(log_file: str = "logs/current/wechat_auto.log", level: int = logging.INFO, config_path: str = "config.json") -> LogManager:
    """设置增强的日志系统（兼容性函数）"""
    config_manager = ConfigManager(config_path)
    log_manager = LogManager(config_manager)
    log_manager.setup_logging(log_file, level)
    return log_manager

def main():
    """测试函数"""
    # 设置日志
    log_manager = setup_logging()

    # 测试配置管理器
    config_manager = ConfigManager()
    print("📋 配置管理器测试:")
    print(f"Excel文件: {config_manager.get('excel_file')}")

    # 测试日志管理器
    print("\n📝 日志管理器测试:")
    print("手动执行日志清理...")
    cleanup_stats = log_manager.cleanup_old_logs()
    print(f"清理统计: {cleanup_stats}")

    # 测试坐标管理器
    coord_manager = CoordinateManager(config_manager)
    print("\n📍 坐标管理器测试:")
    validation = coord_manager.validate_coordinates()
    for element, valid in validation.items():
        status = "✅" if valid else "❌"
        print(f"  {status} {element}")

    # 测试延迟管理器
    delay_manager = DelayManager(config_manager)
    print(f"\n⏳ 延迟管理器测试:")
    print(f"随机延迟: {delay_manager.get_random_delay():.2f}秒")

    # 测试工具函数
    print(f"\n🔧 工具函数测试:")
    test_phone = "13812345678"
    print(f"手机号验证: {test_phone} -> {UtilityFunctions.validate_phone_number(test_phone)}")

if __name__ == "__main__":
    main()
