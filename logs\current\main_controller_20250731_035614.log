2025-07-31 03:56:14,470 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-31 03:56:14,471 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-31 03:56:14,474 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 有效微信窗口统计: 0 个
2025-07-31 03:56:14,475 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 0 个微信窗口
2025-07-31 03:56:14,479 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-31 03:56:14,481 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-31 03:56:14,482 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-31 03:56:14,485 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-31 03:56:14,493 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-31 03:56:14,495 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-31 03:56:14,496 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-31 03:56:14,500 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-31 03:56:14,521 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-31 03:56:14,528 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-31 03:56:14,529 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-31 03:56:14,530 - __main__ - INFO - 🧠 启用智能检测功能...
2025-07-31 03:56:14,531 - __main__ - INFO - ✅ 智能检测功能已启用
2025-07-31 03:56:14,533 - __main__ - INFO - 🔍 支持的检测方法:
2025-07-31 03:56:14,537 - __main__ - INFO -    1. 通过子控件检测确定按钮
2025-07-31 03:56:14,538 - __main__ - INFO -    2. 通过文本识别检测确定按钮
2025-07-31 03:56:14,540 - __main__ - INFO -    3. 通过图像识别检测确定按钮
2025-07-31 03:56:14,546 - __main__ - INFO -    4. 基于相对位置计算确定按钮坐标
2025-07-31 03:56:14,547 - __main__ - INFO - 🖱️ 支持的点击方法:
2025-07-31 03:56:14,548 - __main__ - INFO -    1. 使用Win32 API点击
2025-07-31 03:56:14,549 - __main__ - INFO -    2. 使用PyAutoGUI点击
2025-07-31 03:56:14,550 - __main__ - INFO -    3. 使用键盘Enter键
2025-07-31 03:56:14,551 - __main__ - INFO -    4. 发送窗口消息
2025-07-31 03:56:14,551 - __main__ - INFO - 🔄 重试设置: 检测最多3次, 点击最多4次
2025-07-31 03:56:14,551 - __main__ - INFO - 📱 已启用跨分辨率兼容性
2025-07-31 03:56:14,553 - __main__ - INFO - 🪟 已启用窗口位置无关性
2025-07-31 03:56:14,559 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-31 03:56:14,560 - __main__ - INFO - 📅 当前北京时间: 2025-07-31 11:56:14
2025-07-31 03:56:14,561 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-31 03:56:14,565 - __main__ - INFO - 📅 启动时间: 2025-07-31 11:56:14
2025-07-31 03:56:14,566 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-31 03:56:14,568 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-31 03:56:14,576 - modules.window_manager - INFO - 🎯 总共找到 0 个微信窗口
2025-07-31 03:56:14,577 - __main__ - ERROR - ❌ 未找到任何微信窗口
2025-07-31 03:56:14,577 - __main__ - ERROR - ❌ 未找到微信窗口，程序退出
