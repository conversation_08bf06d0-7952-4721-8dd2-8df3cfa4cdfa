# 日志目录结构说明

## 目录结构
```
logs/
├── README.md                    # 本说明文件
├── current/                     # 当前活跃日志
│   ├── wechat_auto.log         # 主程序日志
│   ├── friend_request.log      # 好友申请日志
│   └── data_manager.log        # 数据管理日志
├── archive/                     # 归档日志
│   ├── 2025/                   # 按年份归档
│   │   └── 07/                 # 按月份归档
│   │       └── 19/             # 按日期归档
│   └── old/                    # 旧版本日志备份
└── temp/                       # 临时日志文件
```

## 日志文件命名规范
- 当前日志：`{module_name}.log`
- 归档日志：`{module_name}_{YYYYMMDD}_{HHMMSS}.log`
- 备份日志：`{original_name}.backup.{timestamp}`

## 日志保留策略
- 当前日志：实时写入，每日轮转
- 归档日志：保留30天
- 临时日志：保留7天
- 备份日志：保留90天

## 自动清理规则
- 每日凌晨2点执行自动清理
- 删除超过保留期的日志文件
- 压缩大于10MB的归档日志
