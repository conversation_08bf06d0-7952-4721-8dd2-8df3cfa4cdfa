#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信自动化系统模块包

本模块包是微信自动化添加好友系统的核心组件集合，提供完整的自动化操作功能。
通过模块化设计，实现了微信界面操作、数据管理、窗口控制等核心功能的解耦和复用。

核心模块说明：
- DataManager: 数据管理模块，负责Excel文件读写、联系人信息管理、处理状态跟踪
- WeChatWindowManager: 窗口管理模块，负责微信窗口检测、激活、切换和多窗口协调
- WeChatMainInterface: 主界面操作模块，负责微信主界面的点击操作和界面状态验证
- FriendRequestProcessor: 好友请求处理模块，负责添加好友窗口的自动化操作和验证信息填写

模块协作关系：
1. ConfigManager提供全局配置管理，为所有模块提供统一的配置参数和设置
2. DataManager提供数据源和状态管理支持，负责Excel文件的读写和联系人信息管理
3. WeChatWindowManager确保操作在正确的微信窗口中执行，提供窗口检测、激活和切换功能
4. WeChatMainInterface执行微信主界面导航和操作，与WeChatWindowManager协作完成界面控制
5. FriendRequestProcessor处理具体的好友添加流程，调用DataManager获取联系人信息
6. FrequencyErrorHandler提供智能错误检测和处理，被FriendRequestProcessor调用处理"操作过于频繁"等错误
7. FriendRequestIntegration提供高级集成功能，协调DataManager和FriendRequestProcessor完成批量处理
8. WeChatAutoAddFriend作为核心自动化引擎，整合上述所有模块完成完整的自动化流程

核心协作流程：
- 配置层：ConfigManager → 所有模块
- 数据层：DataManager ↔ FriendRequestIntegration ↔ FriendRequestProcessor
- 窗口层：WeChatWindowManager ↔ WeChatMainInterface ↔ FriendRequestProcessor
- 错误处理：FrequencyErrorHandler ← FriendRequestProcessor
- 集成层：WeChatAutoAddFriend → 协调所有模块

设计特点：
- 统一的配置管理和日志系统
- 异常处理和错误恢复机制
- 支持多微信窗口并发操作
- 模块间松耦合，便于维护和扩展

版本：2.3.0
作者：AI助手
"""

# 版本信息
__version__ = "2.3.0"
__author__ = "AI助手"

# 导出主要类，方便外部导入
from .data_manager import DataManager
from .friend_request_window import FriendRequestProcessor
from .main_interface import WeChatMainInterface
from .window_manager import WeChatWindowManager
from .frequency_error_handler import FrequencyErrorHandler
from .wechat_auto_add_friend import WeChatAutoAddFriend
from .config_utils import ConfigManager

__all__ = [
    "DataManager",
    "FriendRequestProcessor",
    "WeChatMainInterface",
    "WeChatWindowManager",
    "FrequencyErrorHandler",
    "WeChatAutoAddFriend",
    "ConfigManager"
]




