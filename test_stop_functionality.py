#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
停止功能测试脚本
测试微信自动化程序的停止按钮响应时间和可靠性
"""

import time
import threading
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from main_controller import WeChatMainController

def test_controller_stop_mechanism():
    """测试控制器的停止机制"""
    print("🧪 测试1: 控制器停止机制")
    print("-" * 50)
    
    try:
        # 创建控制器实例
        controller = WeChatMainController("添加好友名单.xlsx", "config.json")
        
        # 测试停止标志初始状态
        print(f"✅ 初始运行状态: {controller.is_running()}")
        print(f"✅ 初始停止请求: {controller.is_stop_requested()}")
        
        # 测试停止请求
        controller.request_stop()
        print(f"✅ 停止请求后运行状态: {controller.is_running()}")
        print(f"✅ 停止请求后停止标志: {controller.is_stop_requested()}")
        
        # 测试重置
        controller.reset_stop_flags()
        print(f"✅ 重置后运行状态: {controller.is_running()}")
        print(f"✅ 重置后停止标志: {controller.is_stop_requested()}")
        
        print("✅ 控制器停止机制测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 控制器停止机制测试失败: {e}")
        return False

def test_stop_response_time():
    """测试停止响应时间"""
    print("\n🧪 测试2: 停止响应时间")
    print("-" * 50)
    
    try:
        controller = WeChatMainController("添加好友名单.xlsx", "config.json")
        
        # 模拟长时间运行的任务
        def long_running_task():
            """模拟长时间运行的任务"""
            controller._is_running = True
            start_time = time.time()
            
            while controller._is_running and not controller._stop_requested:
                time.sleep(0.1)  # 模拟工作
                
                # 检查是否超过10秒（防止无限循环）
                if time.time() - start_time > 10:
                    print("⚠️ 任务超时，自动退出")
                    break
            
            end_time = time.time()
            print(f"📊 任务运行时间: {end_time - start_time:.2f} 秒")
            return end_time - start_time
        
        # 启动任务线程
        task_thread = threading.Thread(target=long_running_task, daemon=True)
        task_thread.start()
        
        # 等待1秒后发送停止信号
        time.sleep(1.0)
        stop_start_time = time.time()
        print("🛑 发送停止信号...")
        controller.request_stop()
        
        # 等待任务结束
        task_thread.join(timeout=5.0)
        stop_end_time = time.time()
        
        response_time = stop_end_time - stop_start_time
        print(f"📊 停止响应时间: {response_time:.2f} 秒")
        
        if response_time <= 2.0:
            print("✅ 停止响应时间测试通过（≤2秒）")
            return True
        else:
            print("❌ 停止响应时间测试失败（>2秒）")
            return False
            
    except Exception as e:
        print(f"❌ 停止响应时间测试失败: {e}")
        return False

def test_gui_stop_integration():
    """测试GUI停止集成（模拟）"""
    print("\n🧪 测试3: GUI停止集成（模拟）")
    print("-" * 50)
    
    try:
        # 模拟GUI停止流程
        class MockGUI:
            def __init__(self):
                self.controller = None
                self.is_running = False
                self.stop_requested = False
                self.automation_thread = None
            
            def start_automation(self):
                """模拟启动自动化"""
                self.is_running = True
                self.stop_requested = False
                self.controller = WeChatMainController("添加好友名单.xlsx", "config.json")
                
                def automation_task():
                    """模拟自动化任务"""
                    self.controller._is_running = True
                    start_time = time.time()
                    
                    while self.is_running and not self.stop_requested:
                        if self.controller.is_stop_requested():
                            print("📡 控制器收到停止信号")
                            break
                        time.sleep(0.1)
                        
                        # 防止无限循环
                        if time.time() - start_time > 10:
                            break
                
                self.automation_thread = threading.Thread(target=automation_task, daemon=True)
                self.automation_thread.start()
                print("🚀 模拟自动化启动")
            
            def stop_automation(self):
                """模拟停止自动化"""
                print("🛑 模拟用户点击停止按钮")
                stop_start_time = time.time()
                
                # 设置停止标志
                self.stop_requested = True
                self.is_running = False
                
                # 通知控制器停止
                if self.controller:
                    self.controller.request_stop()
                
                # 等待线程结束
                if self.automation_thread and self.automation_thread.is_alive():
                    self.automation_thread.join(timeout=2.0)
                
                stop_end_time = time.time()
                response_time = stop_end_time - stop_start_time
                print(f"📊 GUI停止响应时间: {response_time:.2f} 秒")
                
                return response_time
        
        # 执行测试
        mock_gui = MockGUI()
        mock_gui.start_automation()
        
        # 等待1秒后停止
        time.sleep(1.0)
        response_time = mock_gui.stop_automation()
        
        if response_time <= 2.0:
            print("✅ GUI停止集成测试通过")
            return True
        else:
            print("❌ GUI停止集成测试失败")
            return False
            
    except Exception as e:
        print(f"❌ GUI停止集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 微信自动化停止功能测试")
    print("=" * 60)
    
    test_results = []
    
    # 执行所有测试
    test_results.append(test_controller_stop_mechanism())
    test_results.append(test_stop_response_time())
    test_results.append(test_gui_stop_integration())
    
    # 汇总结果
    print("\n📊 测试结果汇总:")
    print("=" * 60)
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    test_names = [
        "控制器停止机制",
        "停止响应时间", 
        "GUI停止集成"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {name}: {status}")
    
    print(f"\n🎯 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！停止功能修复成功！")
        print("💡 停止按钮现在应该能在1-2秒内响应。")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步调试。")
        return False

if __name__ == "__main__":
    main()
