2025-07-31 23:46:37,390 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-07-31 23:46:37,390 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-31 23:46:37,391 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-31 23:46:37,392 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-07-31 23:46:37,396 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 有效微信窗口统计: 1 个
2025-07-31 23:46:37,400 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-31 23:46:37,402 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-31 23:46:37,403 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-31 23:46:37,404 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-31 23:46:37,405 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-31 23:46:37,406 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-31 23:46:37,410 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-31 23:46:37,410 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-31 23:46:37,410 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-31 23:46:37,411 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-31 23:46:37,411 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-31 23:46:37,411 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-31 23:46:37,411 - main_controller - INFO - 🧠 启用智能检测功能...
2025-07-31 23:46:37,412 - main_controller - INFO - ✅ 智能检测功能已启用
2025-07-31 23:46:37,412 - main_controller - INFO - 🔍 支持的检测方法:
2025-07-31 23:46:37,412 - main_controller - INFO -    1. 通过子控件检测确定按钮
2025-07-31 23:46:37,413 - main_controller - INFO -    2. 通过文本识别检测确定按钮
2025-07-31 23:46:37,413 - main_controller - INFO -    3. 通过图像识别检测确定按钮
2025-07-31 23:46:37,414 - main_controller - INFO -    4. 基于相对位置计算确定按钮坐标
2025-07-31 23:46:37,419 - main_controller - INFO - 🖱️ 支持的点击方法:
2025-07-31 23:46:37,419 - main_controller - INFO -    1. 使用Win32 API点击
2025-07-31 23:46:37,419 - main_controller - INFO -    2. 使用PyAutoGUI点击
2025-07-31 23:46:37,420 - main_controller - INFO -    3. 使用键盘Enter键
2025-07-31 23:46:37,421 - main_controller - INFO -    4. 发送窗口消息
2025-07-31 23:46:37,421 - main_controller - INFO - 🔄 重试设置: 检测最多3次, 点击最多4次
2025-07-31 23:46:37,421 - main_controller - INFO - 📱 已启用跨分辨率兼容性
2025-07-31 23:46:37,421 - main_controller - INFO - 🪟 已启用窗口位置无关性
2025-07-31 23:46:37,422 - main_controller - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-31 23:46:37,422 - main_controller - INFO - 📅 当前北京时间: 2025-08-01 07:46:37
