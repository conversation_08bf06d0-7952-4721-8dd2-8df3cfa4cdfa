2025-08-01 15:36:19,372 - INFO - 📝 日志文件: logs\wechat_auto_simple_20250801_153619.log
2025-08-01 15:36:19,373 - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 15:36:19,373 - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-08-01 15:36:19,373 - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-08-01 15:36:25,446 - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 15:36:25,446 - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 15:36:25,447 - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 15:36:25,452 - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-08-01 15:36:25,453 - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-08-01 15:36:25,455 - INFO - 📊 有效微信窗口统计: 0 个
2025-08-01 15:36:25,456 - INFO - 🖥️ 检测到系统中共有 0 个微信窗口
2025-08-01 15:36:25,457 - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 15:36:25,458 - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 15:36:25,462 - INFO - ✅ 微信主界面操作模块初始化完成
2025-08-01 15:36:25,464 - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 15:36:25,465 - INFO - ✅ 已加载配置文件: config.json
2025-08-01 15:36:25,465 - INFO - 🔧 使用固定坐标配置:
2025-08-01 15:36:25,465 - INFO -    📍 验证信息输入框: (960, 330)
2025-08-01 15:36:25,466 - INFO -    📍 备注信息输入框: (960, 450)
2025-08-01 15:36:25,466 - INFO -    📍 确定按钮: (910, 840)
2025-08-01 15:36:25,467 - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-08-01 15:36:25,467 - INFO - ✅ 已设置频率错误处理器引用
2025-08-01 15:36:25,467 - INFO - 🧠 启用智能检测功能...
2025-08-01 15:36:25,468 - INFO - ✅ 智能检测功能已启用
2025-08-01 15:36:25,468 - INFO - 🔍 支持的检测方法:
2025-08-01 15:36:25,468 - INFO - 🖱️ 支持的点击方法:
2025-08-01 15:36:25,469 - INFO - 🔄 重试设置: 检测最多3次, 点击最多4次
2025-08-01 15:36:25,470 - INFO - 📱 已启用跨分辨率兼容性
2025-08-01 15:36:25,470 - INFO - 🪟 已启用窗口位置无关性
2025-08-01 15:36:25,471 - INFO - ✅ 微信自动化主控制器初始化完成
2025-08-01 15:36:25,474 - INFO - 📅 当前北京时间: 2025-08-01 23:36:25
2025-08-01 15:36:25,475 - WARNING - ⚠️ 未找到时间段配置，将使用默认设置
2025-08-01 15:36:31,481 - INFO - 📝 日志文件: logs\wechat_auto_simple_20250801_153631.log
2025-08-01 15:36:31,482 - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 15:36:31,482 - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-08-01 15:36:31,483 - INFO - 📁 Excel文件: 添加好友名单.xlsx
