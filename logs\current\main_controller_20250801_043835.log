2025-08-01 04:38:35,297 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 04:38:35,462 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 04:38:35,533 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 04:38:35,572 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (280x380) - 可能是残留窗口
2025-08-01 04:38:35,596 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 有效微信窗口统计: 2 个
2025-08-01 04:38:35,599 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-08-01 04:38:35,674 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 04:38:35,683 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 04:38:35,708 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-08-01 04:38:35,712 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 04:38:35,717 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-08-01 04:38:35,726 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-08-01 04:38:35,766 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-08-01 04:38:35,774 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-08-01 04:38:35,777 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-08-01 04:38:35,797 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-08-01 04:38:35,805 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-08-01 04:38:35,817 - main_controller - INFO - 🧠 启用智能检测功能...
2025-08-01 04:38:35,850 - main_controller - INFO - ✅ 智能检测功能已启用
2025-08-01 04:38:35,874 - main_controller - INFO - 🔍 支持的检测方法:
2025-08-01 04:38:35,877 - main_controller - INFO -    1. 通过子控件检测确定按钮
2025-08-01 04:38:35,906 - main_controller - INFO -    2. 通过文本识别检测确定按钮
2025-08-01 04:38:35,915 - main_controller - INFO -    3. 通过图像识别检测确定按钮
2025-08-01 04:38:35,916 - main_controller - INFO -    4. 基于相对位置计算确定按钮坐标
2025-08-01 04:38:35,932 - main_controller - INFO - 🖱️ 支持的点击方法:
2025-08-01 04:38:35,939 - main_controller - INFO -    1. 使用Win32 API点击
2025-08-01 04:38:35,944 - main_controller - INFO -    2. 使用PyAutoGUI点击
2025-08-01 04:38:35,944 - main_controller - INFO -    3. 使用键盘Enter键
2025-08-01 04:38:35,946 - main_controller - INFO -    4. 发送窗口消息
2025-08-01 04:38:35,947 - main_controller - INFO - 🔄 重试设置: 检测最多3次, 点击最多4次
2025-08-01 04:38:35,950 - main_controller - INFO - 📱 已启用跨分辨率兼容性
2025-08-01 04:38:35,961 - main_controller - INFO - 🪟 已启用窗口位置无关性
2025-08-01 04:38:35,965 - main_controller - INFO - ✅ 微信自动化主控制器初始化完成
2025-08-01 04:38:35,966 - main_controller - INFO - 📅 当前北京时间: 2025-08-01 12:38:35
2025-08-01 04:38:35,967 - main_controller - INFO - ⏰ 时间段配置:
2025-08-01 04:38:36,001 - main_controller - INFO -    🌅 上午时段: 10:58 - 13:00 (启用: True)
2025-08-01 04:38:36,017 - main_controller - INFO -    🌇 下午时段: 14:00 - 23:59 (启用: True)
2025-08-01 04:38:36,075 - main_controller - INFO - 🕐 正在检查时间权限 - 当前北京时间: 12:38
2025-08-01 04:38:36,085 - main_controller - INFO - 📋 时间段配置检查:
2025-08-01 04:38:36,107 - main_controller - INFO -    🌅 上午时段: 10:58 - 13:00 (启用: True)
2025-08-01 04:38:36,109 - main_controller - INFO -    🌇 下午时段: 14:00 - 23:59 (启用: True)
2025-08-01 04:38:36,111 - main_controller - INFO - ✅ 时间验证通过 - 当前时间 12:38 在上午时段 10:58-13:00 内
2025-08-01 04:38:36,117 - main_controller - INFO - ✅ 当前时间在允许的执行时间段内
2025-08-01 04:38:36,131 - main_controller - INFO - ✅ GUI进度更新回调已设置
