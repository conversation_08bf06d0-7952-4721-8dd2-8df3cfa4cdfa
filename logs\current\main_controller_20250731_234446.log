2025-07-31 23:44:46,321 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-07-31 23:44:46,321 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-31 23:44:46,322 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-31 23:44:46,328 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-07-31 23:44:46,335 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 有效微信窗口统计: 1 个
2025-07-31 23:44:46,341 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-31 23:44:46,351 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-31 23:44:46,354 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-31 23:44:46,357 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-31 23:44:46,367 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-31 23:44:46,370 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-31 23:44:46,376 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-31 23:44:46,377 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-31 23:44:46,378 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-31 23:44:46,380 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-31 23:44:46,380 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-31 23:44:46,381 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-31 23:44:46,381 - main_controller - INFO - 🧠 启用智能检测功能...
2025-07-31 23:44:46,382 - main_controller - INFO - ✅ 智能检测功能已启用
2025-07-31 23:44:46,382 - main_controller - INFO - 🔍 支持的检测方法:
2025-07-31 23:44:46,383 - main_controller - INFO -    1. 通过子控件检测确定按钮
2025-07-31 23:44:46,384 - main_controller - INFO -    2. 通过文本识别检测确定按钮
2025-07-31 23:44:46,384 - main_controller - INFO -    3. 通过图像识别检测确定按钮
2025-07-31 23:44:46,385 - main_controller - INFO -    4. 基于相对位置计算确定按钮坐标
2025-07-31 23:44:46,386 - main_controller - INFO - 🖱️ 支持的点击方法:
2025-07-31 23:44:46,386 - main_controller - INFO -    1. 使用Win32 API点击
2025-07-31 23:44:46,387 - main_controller - INFO -    2. 使用PyAutoGUI点击
2025-07-31 23:44:46,389 - main_controller - INFO -    3. 使用键盘Enter键
2025-07-31 23:44:46,394 - main_controller - INFO -    4. 发送窗口消息
2025-07-31 23:44:46,394 - main_controller - INFO - 🔄 重试设置: 检测最多3次, 点击最多4次
2025-07-31 23:44:46,401 - main_controller - INFO - 📱 已启用跨分辨率兼容性
2025-07-31 23:44:46,401 - main_controller - INFO - 🪟 已启用窗口位置无关性
2025-07-31 23:44:46,402 - main_controller - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-31 23:44:46,403 - main_controller - INFO - 📅 当前北京时间: 2025-08-01 07:44:46
2025-07-31 23:44:46,416 - main_controller - INFO - 🛑 收到停止请求
2025-07-31 23:44:46,513 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-07-31 23:44:46,514 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-31 23:44:46,516 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-31 23:44:46,521 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-07-31 23:44:46,543 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 有效微信窗口统计: 1 个
2025-07-31 23:44:46,546 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-31 23:44:46,553 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-31 23:44:46,555 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-31 23:44:46,558 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-31 23:44:46,561 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-31 23:44:46,565 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-31 23:44:46,568 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-31 23:44:46,569 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-31 23:44:46,570 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-31 23:44:46,570 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-31 23:44:46,571 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-31 23:44:46,582 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-31 23:44:46,587 - main_controller - INFO - 🧠 启用智能检测功能...
2025-07-31 23:44:46,588 - main_controller - INFO - ✅ 智能检测功能已启用
2025-07-31 23:44:46,589 - main_controller - INFO - 🔍 支持的检测方法:
2025-07-31 23:44:46,594 - main_controller - INFO -    1. 通过子控件检测确定按钮
2025-07-31 23:44:46,595 - main_controller - INFO -    2. 通过文本识别检测确定按钮
2025-07-31 23:44:46,596 - main_controller - INFO -    3. 通过图像识别检测确定按钮
2025-07-31 23:44:46,598 - main_controller - INFO -    4. 基于相对位置计算确定按钮坐标
2025-07-31 23:44:46,603 - main_controller - INFO - 🖱️ 支持的点击方法:
2025-07-31 23:44:46,604 - main_controller - INFO -    1. 使用Win32 API点击
2025-07-31 23:44:46,609 - main_controller - INFO -    2. 使用PyAutoGUI点击
2025-07-31 23:44:46,611 - main_controller - INFO -    3. 使用键盘Enter键
2025-07-31 23:44:46,614 - main_controller - INFO -    4. 发送窗口消息
2025-07-31 23:44:46,617 - main_controller - INFO - 🔄 重试设置: 检测最多3次, 点击最多4次
2025-07-31 23:44:46,617 - main_controller - INFO - 📱 已启用跨分辨率兼容性
2025-07-31 23:44:46,618 - main_controller - INFO - 🪟 已启用窗口位置无关性
2025-07-31 23:44:46,619 - main_controller - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-31 23:44:46,622 - main_controller - INFO - 📅 当前北京时间: 2025-08-01 07:44:46
2025-07-31 23:44:47,629 - main_controller - INFO - 🛑 收到停止请求
2025-07-31 23:44:47,638 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-07-31 23:44:47,638 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-31 23:44:47,642 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-31 23:44:47,644 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-07-31 23:44:47,646 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 有效微信窗口统计: 1 个
2025-07-31 23:44:47,646 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-31 23:44:47,656 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-31 23:44:47,658 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-31 23:44:47,659 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-31 23:44:47,664 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-31 23:44:47,666 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-31 23:44:47,666 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-31 23:44:47,667 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-31 23:44:47,667 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-31 23:44:47,668 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-31 23:44:47,668 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-31 23:44:47,669 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-31 23:44:47,669 - main_controller - INFO - 🧠 启用智能检测功能...
2025-07-31 23:44:47,670 - main_controller - INFO - ✅ 智能检测功能已启用
2025-07-31 23:44:47,670 - main_controller - INFO - 🔍 支持的检测方法:
2025-07-31 23:44:47,671 - main_controller - INFO -    1. 通过子控件检测确定按钮
2025-07-31 23:44:47,671 - main_controller - INFO -    2. 通过文本识别检测确定按钮
2025-07-31 23:44:47,671 - main_controller - INFO -    3. 通过图像识别检测确定按钮
2025-07-31 23:44:47,672 - main_controller - INFO -    4. 基于相对位置计算确定按钮坐标
2025-07-31 23:44:47,673 - main_controller - INFO - 🖱️ 支持的点击方法:
2025-07-31 23:44:47,673 - main_controller - INFO -    1. 使用Win32 API点击
2025-07-31 23:44:47,674 - main_controller - INFO -    2. 使用PyAutoGUI点击
2025-07-31 23:44:47,675 - main_controller - INFO -    3. 使用键盘Enter键
2025-07-31 23:44:47,678 - main_controller - INFO -    4. 发送窗口消息
2025-07-31 23:44:47,678 - main_controller - INFO - 🔄 重试设置: 检测最多3次, 点击最多4次
2025-07-31 23:44:47,679 - main_controller - INFO - 📱 已启用跨分辨率兼容性
2025-07-31 23:44:47,679 - main_controller - INFO - 🪟 已启用窗口位置无关性
2025-07-31 23:44:47,680 - main_controller - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-31 23:44:47,681 - main_controller - INFO - 📅 当前北京时间: 2025-08-01 07:44:47
2025-07-31 23:44:48,683 - main_controller - INFO - 🛑 收到停止请求
