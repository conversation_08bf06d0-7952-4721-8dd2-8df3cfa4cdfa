2025-07-31 15:13:19,912 - modules.mouse_visual_feedback - INFO - ✅ 鼠标视觉反馈模块初始化完成
2025-07-31 15:13:19,912 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 鼠标视觉反馈已启用
2025-07-31 15:13:19,913 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-31 15:13:19,913 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-31 15:13:19,916 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-07-31 15:13:19,920 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-07-31 15:13:19,923 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 有效微信窗口统计: 0 个
2025-07-31 15:13:19,924 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 0 个微信窗口
2025-07-31 15:13:19,925 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-31 15:13:19,926 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-31 15:13:19,927 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成（含视觉反馈）
2025-07-31 15:13:19,928 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-31 15:13:19,930 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-31 15:13:19,930 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-31 15:13:19,933 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-31 15:13:19,934 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-31 15:13:19,934 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-31 15:13:19,935 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-31 15:13:19,935 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-31 15:13:19,936 - __main__ - INFO - 🧠 启用智能检测功能...
2025-07-31 15:13:19,936 - __main__ - INFO - ✅ 智能检测功能已启用
2025-07-31 15:13:19,937 - __main__ - INFO - 🔍 支持的检测方法:
2025-07-31 15:13:19,937 - __main__ - INFO -    1. 通过子控件检测确定按钮
2025-07-31 15:13:19,937 - __main__ - INFO -    2. 通过文本识别检测确定按钮
2025-07-31 15:13:19,938 - __main__ - INFO -    3. 通过图像识别检测确定按钮
2025-07-31 15:13:19,941 - __main__ - INFO -    4. 基于相对位置计算确定按钮坐标
2025-07-31 15:13:19,942 - __main__ - INFO - 🖱️ 支持的点击方法:
2025-07-31 15:13:19,943 - __main__ - INFO -    1. 使用Win32 API点击
2025-07-31 15:13:19,943 - __main__ - INFO -    2. 使用PyAutoGUI点击
2025-07-31 15:13:19,944 - __main__ - INFO -    3. 使用键盘Enter键
2025-07-31 15:13:19,945 - __main__ - INFO -    4. 发送窗口消息
2025-07-31 15:13:19,945 - __main__ - INFO - 🔄 重试设置: 检测最多3次, 点击最多4次
2025-07-31 15:13:19,946 - __main__ - INFO - 📱 已启用跨分辨率兼容性
2025-07-31 15:13:19,946 - __main__ - INFO - 🪟 已启用窗口位置无关性
2025-07-31 15:13:19,947 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-31 15:13:19,947 - __main__ - INFO - 📅 当前北京时间: 2025-07-31 23:13:19
2025-07-31 15:13:19,949 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-31 15:13:19,951 - __main__ - INFO - 📅 启动时间: 2025-07-31 23:13:19
2025-07-31 15:13:19,953 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-31 15:13:19,953 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-31 15:13:20,503 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-31 15:13:20,503 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 13765426, 进程: Weixin.exe)
2025-07-31 15:13:21,035 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-31 15:13:21,036 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2427830, 进程: Weixin.exe)
2025-07-31 15:13:21,038 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-31 15:13:21,038 - __main__ - INFO - 📊 窗口扫描统计:
2025-07-31 15:13:21,039 - __main__ - INFO -   🔍 总发现窗口: 2
2025-07-31 15:13:21,039 - __main__ - INFO -   ✅ 有效可用窗口: 2
2025-07-31 15:13:21,039 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-07-31 15:13:21,039 - __main__ - INFO -   👻 隐藏窗口: 0
2025-07-31 15:13:21,040 - __main__ - INFO -   ❌ 无效窗口: 0
2025-07-31 15:13:21,040 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-07-31 15:13:21,040 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 2427830)
2025-07-31 15:13:21,041 - __main__ - INFO -   🔥 窗口 2: 微信 (句柄: 13765426)
2025-07-31 15:13:21,041 - __main__ - INFO - 🎯 开始批量移动所有微信窗口到指定坐标位置...
2025-07-31 15:13:21,043 - __main__ - INFO - 📊 需要移动的窗口数量: 2
2025-07-31 15:13:21,045 - __main__ - INFO - 🔄 移动窗口 1/2: 微信 (句柄: 2427830)
2025-07-31 15:13:21,046 - __main__ - INFO - ✅ 窗口 1 移动成功
2025-07-31 15:13:21,347 - __main__ - INFO - 🔄 移动窗口 2/2: 微信 (句柄: 13765426)
2025-07-31 15:13:21,350 - __main__ - INFO - ✅ 窗口 2 移动成功
2025-07-31 15:13:21,351 - __main__ - INFO - 📊 批量窗口移动完成统计:
2025-07-31 15:13:21,352 - __main__ - INFO -   ✅ 成功移动: 2 个窗口
2025-07-31 15:13:21,352 - __main__ - INFO -   ❌ 移动失败: 0 个窗口
2025-07-31 15:13:21,352 - __main__ - INFO -   📈 成功率: 100.0%
2025-07-31 15:13:21,353 - __main__ - INFO - 🎉 批量窗口移动操作完成，所有窗口已移动到指定坐标位置
2025-07-31 15:13:21,354 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-31 15:13:22,321 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-31 15:13:22,321 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-31 15:13:22,322 - __main__ - INFO - 📋 待处理联系人数: 2810
2025-07-31 15:13:22,323 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-31 15:13:22,323 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2810
2025-07-31 15:13:22,323 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 设置总窗口数量: 2
2025-07-31 15:13:22,324 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-31 15:13:22,324 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-31 15:13:22,325 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-31 15:13:22,326 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2427830, 进程: Weixin.exe)
2025-07-31 15:13:22,327 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-31 15:13:22,327 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 13765426, 进程: Weixin.exe)
2025-07-31 15:13:22,329 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-31 15:13:22,330 - __main__ - INFO - 📊 窗口扫描统计:
2025-07-31 15:13:22,345 - __main__ - INFO -   🔍 总发现窗口: 2
2025-07-31 15:13:22,350 - __main__ - INFO -   ✅ 有效可用窗口: 2
2025-07-31 15:13:22,351 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-07-31 15:13:22,357 - __main__ - INFO -   👻 隐藏窗口: 0
2025-07-31 15:13:22,357 - __main__ - INFO -   ❌ 无效窗口: 0
2025-07-31 15:13:22,358 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-07-31 15:13:22,358 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 2427830)
2025-07-31 15:13:22,359 - __main__ - INFO -   🔥 窗口 2: 微信 (句柄: 13765426)
2025-07-31 15:13:22,361 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 有效微信窗口统计: 2 个
2025-07-31 15:13:22,364 - __main__ - INFO - 
============================================================
2025-07-31 15:13:22,367 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-31 15:13:22,370 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 2427830)
2025-07-31 15:13:22,370 - __main__ - INFO - 📊 全局进度：已处理 0/2810 个联系人（剩余 2810 个）
2025-07-31 15:13:22,371 - __main__ - INFO - ============================================================
2025-07-31 15:13:22,371 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-31 15:13:22,372 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 2427830)
2025-07-31 15:13:22,372 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-31 15:13:22,372 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-31 15:13:22,373 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 2427830)
2025-07-31 15:13:22,373 - __main__ - INFO - 🎯 激活目标窗口: 微信
2025-07-31 15:13:23,174 - __main__ - INFO - 🔝 设置窗口置顶: 微信
2025-07-31 15:13:23,174 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 2427830
2025-07-31 15:13:23,174 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2427830) - 增强版
2025-07-31 15:13:23,479 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-31 15:13:23,480 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-31 15:13:23,480 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-31 15:13:23,481 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-31 15:13:23,481 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-31 15:13:23,482 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-31 15:13:23,482 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-31 15:13:23,483 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-31 15:13:23,483 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-31 15:13:23,484 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-31 15:13:23,686 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-31 15:13:23,686 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-31 15:13:23,688 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 2427830 (API返回: None)
2025-07-31 15:13:23,989 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-31 15:13:23,989 - __main__ - INFO - ✅ 步骤1完成：窗口 微信 已成功激活并置顶
2025-07-31 15:13:23,990 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-31 15:13:24,990 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-31 15:13:24,991 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-31 15:13:24,991 - __main__ - INFO - 🔄 主界面操作尝试 1/3
2025-07-31 15:13:24,991 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-31 15:13:24,991 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-31 15:13:24,992 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-31 15:13:24,992 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-31 15:13:24,992 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-31 15:13:24,993 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-31 15:13:25,193 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-31 15:13:25,194 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-31 15:13:25,195 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-31 15:13:26,696 - modules.mouse_visual_feedback - INFO - 🖱️ 开始移动鼠标到 微信按钮: (1021, 428) -> (31, 95)
2025-07-31 15:13:28,248 - modules.mouse_visual_feedback - INFO - ✅ 鼠标移动完成: 微信按钮
2025-07-31 15:13:28,467 - modules.mouse_visual_feedback - INFO - 🎯 显示点击效果: 微信按钮 at (31, 95)
2025-07-31 15:13:28,668 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-31 15:13:28,669 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-31 15:13:28,669 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
2025-07-31 15:13:31,497 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-31 15:13:31,698 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-31 15:13:31,698 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-31 15:13:31,699 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-31 15:13:33,200 - modules.mouse_visual_feedback - INFO - 🖱️ 开始移动鼠标到 通讯录按钮: (31, 95) -> (29, 144)
2025-07-31 15:13:34,645 - modules.mouse_visual_feedback - INFO - ✅ 鼠标移动完成: 通讯录按钮
2025-07-31 15:13:34,853 - modules.mouse_visual_feedback - INFO - 🎯 显示点击效果: 通讯录按钮 at (29, 144)
2025-07-31 15:13:35,054 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-31 15:13:35,054 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-31 15:13:35,055 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-31 15:13:36,753 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-31 15:13:36,953 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-31 15:13:36,954 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-31 15:13:36,954 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-31 15:13:38,455 - modules.mouse_visual_feedback - INFO - 🖱️ 开始移动鼠标到 微信主按钮: (29, 144) -> (31, 95)
2025-07-31 15:13:39,902 - modules.mouse_visual_feedback - INFO - ✅ 鼠标移动完成: 微信主按钮
2025-07-31 15:13:40,106 - modules.mouse_visual_feedback - INFO - 🎯 显示点击效果: 微信主按钮 at (31, 95)
2025-07-31 15:13:40,308 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-31 15:13:40,309 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-31 15:13:40,311 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-31 15:13:42,659 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-31 15:13:42,860 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤4: 点击+快捷操作按钮...
2025-07-31 15:13:42,860 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-31 15:13:42,861 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-31 15:13:44,362 - modules.mouse_visual_feedback - INFO - 🖱️ 开始移动鼠标到 +快捷操作按钮: (31, 95) -> (244, 41)
2025-07-31 15:13:45,806 - modules.mouse_visual_feedback - INFO - ✅ 鼠标移动完成: +快捷操作按钮
2025-07-31 15:13:46,016 - modules.mouse_visual_feedback - INFO - 🎯 显示点击效果: +快捷操作按钮 at (244, 41)
2025-07-31 15:13:46,217 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-31 15:13:46,218 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-31 15:13:46,218 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-31 15:13:47,895 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-31 15:13:48,097 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤5: 点击添加朋友选项...
2025-07-31 15:13:48,098 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-31 15:13:48,099 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
