#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
鼠标移动基础操作模块
功能：提供基础的鼠标移动操作，已禁用所有视觉效果

核心特性：
1. 基础鼠标移动
2. 简单点击操作
3. 无视觉干扰

版本：2.0.0 (视觉效果已禁用)
作者：AI助手
修改时间：2025-01-31
"""

import time
from typing import Tuple, Optional
import pyautogui
import logging


class MouseVisualFeedback:
    """鼠标基础操作类（已禁用所有视觉效果）"""

    def __init__(self, config: dict = None):
        """初始化基础鼠标操作"""
        self.logger = logging.getLogger(__name__)

        # 配置（保留兼容性，但不使用视觉效果）
        self.config = config or {}

        # 禁用所有视觉效果
        self.is_running = True

        self.logger.info("✅ 鼠标基础操作模块初始化完成（视觉效果已禁用）")
    
    def enhanced_move_to(self, start_pos: Tuple[int, int], end_pos: Tuple[int, int],
                        duration: float = 0.3, element_name: str = "") -> bool:
        """基础鼠标移动（已禁用视觉效果）"""
        try:
            self.logger.info(f"🖱️ 移动鼠标到 {element_name}: {end_pos}")

            # 直接移动到目标位置，无视觉效果
            pyautogui.moveTo(end_pos[0], end_pos[1], duration=duration)

            self.logger.info(f"✅ 鼠标移动完成: {element_name}")
            return True

        except Exception as e:
            self.logger.error(f"❌ 鼠标移动失败: {e}")
            return False

    def show_click_effect(self, pos: Tuple[int, int], element_name: str = ""):
        """显示点击效果（已禁用）"""
        # 不执行任何视觉效果，只记录日志
        self.logger.debug(f"点击效果已禁用: {element_name} at {pos}")

    def cleanup_all(self):
        """清理所有视觉效果（已禁用）"""
        # 无需清理，因为没有创建任何视觉效果
        self.logger.debug("视觉效果清理（无操作）")

    def update_config(self, new_config: dict):
        """更新配置"""
        self.config.update(new_config)
        self.logger.debug("配置已更新（视觉效果已禁用）")


# 全局实例
_mouse_feedback_instance = None

def get_mouse_feedback_instance(config: dict = None) -> MouseVisualFeedback:
    """获取鼠标视觉反馈实例（单例模式）"""
    global _mouse_feedback_instance
    if _mouse_feedback_instance is None:
        _mouse_feedback_instance = MouseVisualFeedback(config)
    return _mouse_feedback_instance
