#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据读取管理模块
功能：Excel数据读取、状态记录、日志管理等
"""

import pandas as pd
import logging
import json
import os
import time
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Optional, Any
import openpyxl
from openpyxl.styles import PatternFill, Font

class DataManager:
    """数据管理器"""

    def __init__(self, config_path: str = "config.json"):
        # 🔧 修复：使用唯一的logger名称，避免重复输出
        logger_name = f'data_manager_{id(self)}'
        self.logger = logging.getLogger(logger_name)
        # 🔧 关键修复：防止向父logger传播
        self.logger.propagate = False
        self.config = self._load_config(config_path)

        # 文件路径
        self.excel_file = self.config.get("excel_file", "添加好友名单.xlsx")
        self.log_file = self.config.get("log_file", "logs/wechat_auto.log")
        self.results_dir = "results"

        # 确保目录存在
        os.makedirs(os.path.dirname(self.log_file), exist_ok=True)
        os.makedirs(self.results_dir, exist_ok=True)

        # 北京时区
        self.beijing_tz = timezone(timedelta(hours=8))
        
        # 状态列名 - 与Excel文件列名完全匹配
        self.status_columns = {
            "phone": "手机号码",
            "name": "姓名",
            "id_card": "身份证",
            "exam_number": "准考证",
            "verification": "验证信息",
            "status": "处理状态",
            "message": "处理结果",
            "timestamp": "处理时间",
            "window_index": "微信窗口",
            "retry_count": "重试次数"
        }
        
        # 状态映射 - 标准化处理状态
        self.status_mapping = {
            "pending": "待处理",
            "processing": "处理中",
            "success": "成功",
            "error": "失败",
            "user_not_found": "失败",
            "already_friend": "成功",
            "frequent_operation": "失败",
            "skipped": "失败",
            "timeout": "失败",
            "network_error": "失败",
            "verification_failed": "失败",
            "add_to_contacts": "成功",
            "已添加到通讯录": "成功",
            "好友申请已发送": "成功"
        }

        # 处理结果映射 - 标准化处理结果描述
        self.result_mapping = {
            "success": "好友申请已发送",
            "add_to_contacts": "好友申请已发送",
            "已添加到通讯录": "好友申请已发送",
            "already_friend": "已经是好友",
            "user_not_found": "用户不存在",
            "frequent_operation": "操作频繁",
            "timeout": "操作超时",
            "network_error": "网络异常",
            "verification_failed": "验证失败",
            "error": "操作失败",
            "skipped": "已跳过"
        }
        
        self.logger.info("✅ 数据管理模块初始化完成")
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            self.logger.info(f"✅ 配置文件加载成功: {config_path}")
            return config
        except Exception as e:
            self.logger.error(f"❌ 配置文件加载失败: {e}")
            return {}
    
    def load_phone_numbers(self) -> List[Dict]:
        """从Excel文件加载手机号列表"""
        try:
            if not os.path.exists(self.excel_file):
                self.logger.error(f"❌ Excel文件不存在: {self.excel_file}")
                return []
            
            # 读取文件（支持Excel和CSV，自动检测编码）
            if self.excel_file.endswith('.csv'):
                # CSV文件编码自动检测
                encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1']
                df = None
                for encoding in encodings:
                    try:
                        df = pd.read_csv(self.excel_file, encoding=encoding)
                        self.logger.info(f"✅ 成功使用 {encoding} 编码读取CSV文件")
                        break
                    except UnicodeDecodeError:
                        continue
                    except Exception as e:
                        self.logger.warning(f"⚠️ 使用 {encoding} 编码读取失败: {e}")
                        continue

                if df is None:
                    raise Exception("无法使用任何编码格式读取CSV文件")
            else:
                df = pd.read_excel(self.excel_file)
            
            # 确保所有必要的列都存在
            required_columns = list(self.status_columns.values())
            for col_name in required_columns:
                if col_name not in df.columns:
                    df[col_name] = ""
                    self.logger.info(f"✅ 添加缺失列: {col_name}")

            # 查找手机号列
            phone_column = self.status_columns["phone"]  # 直接使用"手机号码"

            if phone_column not in df.columns:
                self.logger.error(f"❌ 未找到手机号列: {phone_column}")
                return []

            # 提取手机号数据
            phone_numbers = []
            for index, row in df.iterrows():
                phone = str(row[phone_column]).strip()
                if phone and phone != 'nan' and phone != '手机号码':  # 跳过表头
                    # 处理状态字段 - 正确处理NaN值
                    status_value = row.get(self.status_columns["status"], "")
                    if pd.isna(status_value) or str(status_value).strip() in ['', 'nan', 'NaN', 'None']:
                        status = "pending"
                    else:
                        status = str(status_value).strip()

                    phone_data = {
                        "index": index,
                        "phone": phone,
                        "name": str(row.get(self.status_columns["name"], "")).strip(),
                        "id_card": str(row.get(self.status_columns["id_card"], "")).strip(),
                        "exam_number": str(row.get(self.status_columns["exam_number"], "")).strip(),
                        "verification": str(row.get(self.status_columns["verification"], "")).strip(),
                        "status": status,
                        "message": str(row.get(self.status_columns["message"], "")).strip(),
                        "timestamp": str(row.get(self.status_columns["timestamp"], "")).strip(),
                        "window_index": row.get(self.status_columns["window_index"], 0),
                        "retry_count": row.get(self.status_columns["retry_count"], 0)
                    }
                    phone_numbers.append(phone_data)
            
            # 🔧 修复：只在数据量变化时输出日志，避免频繁重复输出
            if not hasattr(self, '_last_loaded_count') or self._last_loaded_count != len(phone_numbers):
                self.logger.info(f"✅ 成功加载 {len(phone_numbers)} 个手机号")
                self._last_loaded_count = len(phone_numbers)

            return phone_numbers
            
        except Exception as e:
            self.logger.error(f"❌ 加载手机号失败: {e}")
            return []
    
    def update_phone_status(self, phone: str, status: str, message: str = "", 
                          window_index: int = 0, retry_count: int = 0) -> bool:
        """更新手机号处理状态"""
        try:
            if not os.path.exists(self.excel_file):
                self.logger.error(f"❌ Excel文件不存在: {self.excel_file}")
                return False
            
            # 读取文件（支持Excel和CSV，自动检测编码）
            if self.excel_file.endswith('.csv'):
                # CSV文件编码自动检测
                encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1']
                df = None
                for encoding in encodings:
                    try:
                        df = pd.read_csv(self.excel_file, encoding=encoding)
                        self.logger.info(f"✅ 成功使用 {encoding} 编码读取CSV文件")
                        break
                    except UnicodeDecodeError:
                        continue
                    except Exception as e:
                        self.logger.warning(f"⚠️ 使用 {encoding} 编码读取失败: {e}")
                        continue

                if df is None:
                    raise Exception("无法使用任何编码格式读取CSV文件")
            else:
                df = pd.read_excel(self.excel_file)
            
            # 使用正确的手机号列名
            phone_column = self.status_columns["phone"]  # "手机号码"

            # 确保所有状态列存在
            for col_key, col_name in self.status_columns.items():
                if col_name not in df.columns:
                    df[col_name] = ""
                    self.logger.info(f"✅ 添加缺失列: {col_name}")

            # 查找并更新对应行
            phone_found = False
            for index, row in df.iterrows():
                if str(row[phone_column]).strip() == phone:
                    # 更新状态信息 - 使用北京时间和标准化状态
                    standardized_status = self.status_mapping.get(status, status)
                    standardized_message = self.result_mapping.get(status, message) if message == "" else message
                    beijing_time = datetime.now(self.beijing_tz).strftime("%Y-%m-%d %H:%M:%S")

                    df.at[index, self.status_columns["status"]] = standardized_status
                    df.at[index, self.status_columns["message"]] = standardized_message
                    df.at[index, self.status_columns["timestamp"]] = beijing_time
                    df.at[index, self.status_columns["window_index"]] = window_index
                    df.at[index, self.status_columns["retry_count"]] = retry_count
                    phone_found = True
                    self.logger.info(f"📝 更新手机号 {phone} 状态: {status} -> {message}")
                    break
            
            if not phone_found:
                self.logger.warning(f"⚠️ 未找到手机号: {phone}")
                return False
            
            # 保存Excel文件
            df.to_excel(self.excel_file, index=False)
            
            self.logger.info(f"✅ 更新状态成功: {phone} -> {status}")
            return True

        except Exception as e:
            self.logger.error(f"❌ 更新状态失败: {e}")
            return False

    def batch_update_status(self, updates: List[Dict]) -> bool:
        """批量更新手机号状态

        Args:
            updates: 更新列表，每个元素包含 {phone, status, message, window_index, retry_count}

        Returns:
            bool: 是否成功
        """
        try:
            if not os.path.exists(self.excel_file):
                self.logger.error(f"❌ Excel文件不存在: {self.excel_file}")
                return False

            # 读取Excel文件
            df = pd.read_excel(self.excel_file)
            phone_column = self.status_columns["phone"]

            # 确保所有状态列存在
            for col_key, col_name in self.status_columns.items():
                if col_name not in df.columns:
                    df[col_name] = ""

            # 批量更新
            updated_count = 0
            for update in updates:
                phone = update.get("phone", "")
                status = update.get("status", "")
                message = update.get("message", "")
                window_index = update.get("window_index", 0)
                retry_count = update.get("retry_count", 0)

                # 查找并更新对应行 - 使用北京时间和标准化状态
                for index, row in df.iterrows():
                    if str(row[phone_column]).strip() == phone:
                        standardized_status = self.status_mapping.get(status, status)
                        standardized_message = self.result_mapping.get(status, message) if message == "" else message
                        beijing_time = datetime.now(self.beijing_tz).strftime("%Y-%m-%d %H:%M:%S")

                        df.at[index, self.status_columns["status"]] = standardized_status
                        df.at[index, self.status_columns["message"]] = standardized_message
                        df.at[index, self.status_columns["timestamp"]] = beijing_time
                        df.at[index, self.status_columns["window_index"]] = window_index
                        df.at[index, self.status_columns["retry_count"]] = retry_count
                        updated_count += 1
                        break

            # 保存Excel文件
            df.to_excel(self.excel_file, index=False)

            self.logger.info(f"✅ 批量更新成功: {updated_count}/{len(updates)} 条记录")
            return True

        except Exception as e:
            self.logger.error(f"❌ 批量更新失败: {e}")
            return False
    
    def get_pending_phones(self) -> List[Dict]:
        """获取待处理的手机号"""
        all_phones = self.load_phone_numbers()
        pending_phones = []

        for phone in all_phones:
            status = phone["status"]

            # 处理NaN值和各种空值情况
            if pd.isna(status) or status is None:
                status_str = ""
            else:
                status_str = str(status).strip()

            # 检查是否为待处理状态
            if status_str in ["pending", "待处理", "", "nan"]:
                pending_phones.append(phone)

        self.logger.info(f"📋 待处理手机号数量: {len(pending_phones)}")
        return pending_phones
    
    def get_statistics(self) -> Dict:
        """获取处理统计信息 - 修复版"""
        all_phones = self.load_phone_numbers()

        if not all_phones:
            return {
                "total": 0,
                "pending": 0,
                "success": 0,
                "error": 0,
                "processed": 0,
                "user_not_found": 0,
                "already_friend": 0,
                "frequent_operation": 0,
                "add_success": 0,
                "other": 0
            }

        stats = {
            "total": len(all_phones),
            "pending": 0,
            "success": 0,
            "error": 0,
            "processed": 0,
            "user_not_found": 0,
            "already_friend": 0,
            "frequent_operation": 0,
            "add_success": 0,
            "other": 0
        }

        for phone in all_phones:
            status = phone["status"]
            message = phone.get("message", "")

            # 处理NaN值和各种空值情况 - 与GUI保持完全一致
            if pd.isna(status) or status is None:
                status_str = ""
            else:
                status_str = str(status).strip()

            # 标准化状态判断 - 与GUI过滤逻辑完全一致
            # 1. 待处理状态判断（与view_pending_contacts保持一致）
            if (pd.isna(status) or
                status is None or
                status_str in ['', 'pending', '待处理', 'nan']):
                stats["pending"] += 1

            # 2. 成功状态判断
            elif status_str in ["成功", "success", "已添加到通讯录", "add_to_contacts", "已是好友", "already_friend"]:
                stats["success"] += 1
                stats["processed"] += 1
                # 根据处理结果进一步分类
                if status_str in ["已添加到通讯录", "add_to_contacts"] or "好友申请已发送" in str(message) or "已添加到通讯录" in str(message):
                    stats["add_success"] += 1
                elif status_str in ["已是好友", "already_friend"] or "已经是好友" in str(message) or "已是好友" in str(message):
                    stats["already_friend"] += 1

            # 3. 失败状态判断（与view_failed_contacts保持一致）
            elif status_str in ['失败', 'error', '错误', '用户不存在', 'user_not_found',
                               '操作频繁', 'frequent_operation', 'timeout', 'network_error',
                               'verification_failed', 'skipped']:
                stats["error"] += 1
                stats["processed"] += 1
                # 根据具体状态进一步分类
                if status_str in ["用户不存在", "user_not_found"] or "用户不存在" in str(message):
                    stats["user_not_found"] += 1
                elif status_str in ["操作频繁", "frequent_operation"] or "操作频繁" in str(message):
                    stats["frequent_operation"] += 1

            else:
                # 其他状态也算作已处理
                stats["other"] += 1
                stats["processed"] += 1

        # 验证数据一致性
        calculated_processed = stats["success"] + stats["error"] + stats["other"]
        if calculated_processed != stats["processed"]:
            self.logger.warning(f"⚠️ 统计数据不一致: 计算的已处理数({calculated_processed}) != 统计的已处理数({stats['processed']})")
            stats["processed"] = calculated_processed

        self.logger.info(f"📊 统计结果: 总数={stats['total']}, 待处理={stats['pending']}, 已处理={stats['processed']}, 成功={stats['success']}, 失败={stats['error']}")

        return stats

    def refresh_and_recount_statistics(self) -> Dict:
        """刷新数据并重新统计 - 强制重新加载Excel数据"""
        try:
            self.logger.info("🔄 开始刷新数据并重新统计...")

            # 强制重新加载数据
            stats = self.get_statistics()

            # 记录详细统计信息
            self.logger.info("📈 详细统计信息:")
            self.logger.info(f"   📋 总联系人数: {stats['total']}")
            self.logger.info(f"   ⏳ 待处理: {stats['pending']}")
            self.logger.info(f"   📊 已处理: {stats['processed']}")
            self.logger.info(f"   ✅ 成功: {stats['success']}")
            self.logger.info(f"   ❌ 失败: {stats['error']}")
            self.logger.info(f"   👤 用户不存在: {stats['user_not_found']}")
            self.logger.info(f"   👥 已是好友: {stats['already_friend']}")
            self.logger.info(f"   ⚠️ 操作频繁: {stats['frequent_operation']}")
            self.logger.info(f"   📞 添加成功: {stats['add_success']}")
            self.logger.info(f"   🔄 其他状态: {stats['other']}")

            return stats

        except Exception as e:
            self.logger.error(f"❌ 刷新统计失败: {e}")
            return {"total": 0, "pending": 0, "success": 0, "error": 0, "processed": 0}

    def fix_existing_data_format(self) -> bool:
        """修复现有数据格式 - 标准化状态和结果，修复时间格式"""
        try:
            self.logger.info("🔧 开始修复现有数据格式...")

            if not os.path.exists(self.excel_file):
                self.logger.error(f"❌ Excel文件不存在: {self.excel_file}")
                return False

            # 读取Excel文件
            df = pd.read_excel(self.excel_file)
            self.logger.info(f"📂 读取Excel文件成功，共 {len(df)} 行数据")

            # 确保必要的列存在
            required_columns = [
                self.status_columns["status"],
                self.status_columns["message"],
                self.status_columns["timestamp"]
            ]

            for col in required_columns:
                if col not in df.columns:
                    df[col] = ""
                    self.logger.info(f"➕ 添加缺失列: {col}")

            fixed_count = 0
            beijing_tz = timezone(timedelta(hours=8))

            for index, row in df.iterrows():
                # 确保 index 是整数类型以支持算术运算
                row_number = int(index)  # type: ignore
                original_status = str(row.get(self.status_columns["status"], "")).strip()
                original_message = str(row.get(self.status_columns["message"], "")).strip()
                original_timestamp = str(row.get(self.status_columns["timestamp"], "")).strip()

                # 标准化状态
                if original_status in ["已添加到通讯录", "add_to_contacts"]:
                    new_status = "成功"
                    new_message = "好友申请已发送" if original_message == "" else original_message
                elif original_status in ["用户不存在", "user_not_found"]:
                    new_status = "失败"
                    new_message = "用户不存在" if original_message == "" else original_message
                elif original_status in ["已是好友", "already_friend"]:
                    new_status = "成功"
                    new_message = "已经是好友" if original_message == "" else original_message
                elif original_status in ["操作频繁", "frequent_operation"]:
                    new_status = "失败"
                    new_message = "操作频繁" if original_message == "" else original_message
                elif original_status in ["错误", "error"]:
                    new_status = "失败"
                    new_message = "操作失败" if original_message == "" else original_message
                elif original_status == "成功":
                    new_status = "成功"
                    new_message = "好友申请已发送" if original_message == "" else original_message
                else:
                    # 保持原状态不变
                    new_status = original_status
                    new_message = original_message

                # 修复时间格式 - 如果时间不为空且不是标准格式，则更新为当前北京时间
                if original_timestamp and original_timestamp not in ["", "nan", "NaT"]:
                    try:
                        # 尝试解析现有时间格式
                        parsed_time = pd.to_datetime(original_timestamp)
                        # 如果解析成功，保持原时间但确保格式正确
                        new_timestamp = parsed_time.strftime("%Y-%m-%d %H:%M:%S")
                    except:
                        # 如果解析失败，使用当前北京时间
                        new_timestamp = datetime.now(beijing_tz).strftime("%Y-%m-%d %H:%M:%S")
                        self.logger.warning(f"⚠️ 第{row_number+1}行时间格式异常，已更新为当前时间")
                else:
                    new_timestamp = original_timestamp

                # 检查是否需要更新
                if (new_status != original_status or
                    new_message != original_message or
                    new_timestamp != original_timestamp):

                    df.at[index, self.status_columns["status"]] = new_status
                    df.at[index, self.status_columns["message"]] = new_message
                    if new_timestamp != original_timestamp:
                        df.at[index, self.status_columns["timestamp"]] = new_timestamp

                    fixed_count += 1
                    self.logger.info(f"🔧 修复第{row_number+1}行: {original_status} -> {new_status}")

            if fixed_count > 0:
                # 保存修复后的数据
                df.to_excel(self.excel_file, index=False)
                self.logger.info(f"✅ 数据修复完成，共修复 {fixed_count} 行数据")
            else:
                self.logger.info("ℹ️ 数据格式正确，无需修复")

            return True

        except Exception as e:
            self.logger.error(f"❌ 修复数据格式失败: {e}")
            return False
    
    def export_results(self, filename: Optional[str] = None) -> str:
        """导出处理结果"""
        if filename is None:
            timestamp = datetime.now(self.beijing_tz).strftime("%Y%m%d_%H%M%S")
            filename = f"wechat_results_{timestamp}.xlsx"
        
        export_path = os.path.join(self.results_dir, filename)
        
        try:
            # 加载数据
            all_phones = self.load_phone_numbers()
            
            if not all_phones:
                self.logger.error("❌ 没有数据可导出")
                return ""
            
            # 创建DataFrame
            df = pd.DataFrame(all_phones)
            
            # 重命名列
            column_mapping = {
                "phone": "手机号",
                "status": "处理状态",
                "message": "处理结果",
                "timestamp": "处理时间",
                "window_index": "微信窗口",
                "retry_count": "重试次数"
            }
            df = df.rename(columns=column_mapping)
            
            # 保存到Excel
            with pd.ExcelWriter(export_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='处理结果', index=False)
                
                # 获取工作表
                worksheet = writer.sheets['处理结果']
                
                # 设置列宽
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
                
                # 设置标题行样式
                header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                header_font = Font(color="FFFFFF", bold=True)
                
                for cell in worksheet[1]:
                    cell.fill = header_fill
                    cell.font = header_font
            
            self.logger.info(f"✅ 结果导出成功: {export_path}")
            return export_path
            
        except Exception as e:
            self.logger.error(f"❌ 导出结果失败: {e}")
            return ""
    
    def backup_data(self) -> str:
        """备份数据"""
        timestamp = datetime.now(self.beijing_tz).strftime("%Y%m%d_%H%M%S")
        backup_filename = f"backup_{timestamp}.xlsx"
        backup_path = os.path.join(self.results_dir, backup_filename)
        
        try:
            if os.path.exists(self.excel_file):
                import shutil
                shutil.copy2(self.excel_file, backup_path)
                self.logger.info(f"✅ 数据备份成功: {backup_path}")
                return backup_path
            else:
                self.logger.error("❌ 原始文件不存在，无法备份")
                return ""
                
        except Exception as e:
            self.logger.error(f"❌ 数据备份失败: {e}")
            return ""
    
    def log_operation(self, operation: str, details: Dict) -> None:
        """记录操作日志"""
        try:
            log_entry = {
                "timestamp": datetime.now(self.beijing_tz).strftime("%Y-%m-%d %H:%M:%S"),
                "operation": operation,
                "details": details
            }
            
            # 写入日志文件
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
            
            self.logger.info(f"📝 操作日志已记录: {operation}")
            
        except Exception as e:
            self.logger.error(f"❌ 记录日志失败: {e}")
    
    def get_progress_report(self) -> str:
        """获取进度报告"""
        stats = self.get_statistics()
        
        if stats["total"] == 0:
            return "❌ 没有数据"
        
        processed = stats["total"] - stats["pending"]
        progress_percent = (processed / stats["total"]) * 100
        
        report = f"""
📊 处理进度报告
{'='*50}
总数量: {stats['total']}
已处理: {processed} ({progress_percent:.1f}%)
待处理: {stats['pending']}

📈 处理结果统计:
✅ 成功添加: {stats['success']}
❌ 用户不存在: {stats['user_not_found']}
👥 已是好友: {stats['already_friend']}
⚠️ 操作频繁: {stats['frequent_operation']}
🔴 错误: {stats['error']}
📋 其他: {stats['other']}
"""
        return report

    def cleanup(self):
        """清理资源"""
        try:
            self.logger.info("🧹 数据管理器开始清理资源...")
            # 这里可以添加清理逻辑，比如关闭文件句柄、清理缓存等
            self.logger.info("✅ 数据管理器资源清理完成")
        except Exception as e:
            self.logger.error(f"❌ 清理数据管理器资源时出错: {e}")

def main():
    """测试函数"""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    data_manager = DataManager()
    
    # 测试加载手机号
    print("📱 测试加载手机号...")
    phones = data_manager.load_phone_numbers()
    print(f"加载到 {len(phones)} 个手机号")
    
    # 显示统计信息
    print("\n📊 统计信息:")
    stats = data_manager.get_statistics()
    print(stats)
    
    # 显示进度报告
    print(data_manager.get_progress_report())

if __name__ == "__main__":
    main()
