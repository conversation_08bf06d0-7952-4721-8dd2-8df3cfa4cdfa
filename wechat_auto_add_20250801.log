2025-08-01 00:27:34,808 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:40:08,563 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:40:32,529 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:42:00,154 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,099 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,291 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,291 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,373 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,373 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,373 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,550 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,550 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,550 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,550 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,674 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,674 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,674 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,674 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,674 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,771 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,771 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,771 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,771 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,771 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,771 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,903 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,903 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,903 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,903 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,903 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,903 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 00:55:16,903 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 02:30:37,327 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 02:31:36,326 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 02:43:01,071 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 03:16:02,400 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 04:26:41,542 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 04:27:12,580 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 04:27:12,580 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 04:27:12,582 - WeChatAutoAdd - INFO - screenshots目录已经是干净的
2025-08-01 04:27:12,582 - WeChatAutoAdd - INFO - screenshots目录已经是干净的
2025-08-01 04:27:12,583 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 04:27:12,583 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 04:27:12,588 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 04:27:12,588 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 04:27:12,597 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 04:27:12,597 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 04:27:12,599 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 04:27:12,599 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 04:27:12,601 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 04:27:12,601 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 04:27:12,602 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 04:27:12,602 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 04:27:12,603 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-08-01 04:27:12,603 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-08-01 04:27:12,604 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 04:27:12,604 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 04:27:13,112 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 04:27:13,112 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 04:27:13,113 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 04:27:13,113 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 04:27:13,205 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差40.69, 边缘比例0.0530
2025-08-01 04:27:13,205 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差40.69, 边缘比例0.0530
2025-08-01 04:27:13,218 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_042713.png
2025-08-01 04:27:13,218 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_042713.png
2025-08-01 04:27:13,232 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 04:27:13,232 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 04:27:13,242 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 04:27:13,242 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 04:27:13,253 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 04:27:13,253 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 04:27:13,272 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 04:27:13,272 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 04:27:13,300 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 04:27:13,300 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 04:27:13,306 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_042713.png
2025-08-01 04:27:13,306 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_042713.png
2025-08-01 04:27:13,335 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-08-01 04:27:13,335 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-08-01 04:27:13,358 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 04:27:13,358 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 04:27:13,367 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 04:27:13,367 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 04:27:13,369 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,323), 尺寸128x30, 长宽比4.27, 已知特征:True
2025-08-01 04:27:13,369 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,323), 尺寸128x30, 长宽比4.27, 已知特征:True
2025-08-01 04:27:13,373 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=323 (距底部154像素区域)
2025-08-01 04:27:13,373 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=323 (距底部154像素区域)
2025-08-01 04:27:13,376 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-08-01 04:27:13,376 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-08-01 04:27:13,391 - WeChatAutoAdd - INFO - 发现已知按钮特征候选: 位置(100,323), 尺寸128x30
2025-08-01 04:27:13,391 - WeChatAutoAdd - INFO - 发现已知按钮特征候选: 位置(100,323), 尺寸128x30
2025-08-01 04:27:13,402 - WeChatAutoAdd - INFO - 发现已知按钮特征: 位置(100,323), 尺寸128x30
2025-08-01 04:27:13,402 - WeChatAutoAdd - INFO - 发现已知按钮特征: 位置(100,323), 尺寸128x30
2025-08-01 04:27:13,405 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,259), 尺寸8x4, 长宽比2.00, 面积32
2025-08-01 04:27:13,405 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,259), 尺寸8x4, 长宽比2.00, 面积32
2025-08-01 04:27:13,421 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,258), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 04:27:13,421 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,258), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 04:27:13,436 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(49,257), 尺寸1x5, 长宽比0.20, 面积5
2025-08-01 04:27:13,436 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(49,257), 尺寸1x5, 长宽比0.20, 面积5
2025-08-01 04:27:13,440 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(47,257), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 04:27:13,440 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(47,257), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 04:27:13,443 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(71,256), 尺寸2x3, 长宽比0.67, 面积6
2025-08-01 04:27:13,443 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(71,256), 尺寸2x3, 长宽比0.67, 面积6
2025-08-01 04:27:13,476 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,255), 尺寸2x3, 长宽比0.67, 面积6
2025-08-01 04:27:13,476 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,255), 尺寸2x3, 长宽比0.67, 面积6
2025-08-01 04:27:13,503 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,254), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 04:27:13,503 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,254), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 04:27:13,505 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,253), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 04:27:13,505 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,253), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 04:27:13,508 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,253), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 04:27:13,508 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,253), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 04:27:13,510 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(55,251), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 04:27:13,510 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(55,251), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 04:27:13,520 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,249), 尺寸34x35, 长宽比0.97, 面积1190
2025-08-01 04:27:13,520 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,249), 尺寸34x35, 长宽比0.97, 面积1190
2025-08-01 04:27:13,534 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,249), 尺寸34x35, 长宽比0.97, 面积1190
2025-08-01 04:27:13,534 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,249), 尺寸34x35, 长宽比0.97, 面积1190
2025-08-01 04:27:13,536 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-08-01 04:27:13,536 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-08-01 04:27:13,540 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=60.9 (阈值:60)
2025-08-01 04:27:13,540 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=60.9 (阈值:60)
2025-08-01 04:27:13,541 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-08-01 04:27:13,541 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-08-01 04:27:13,543 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=61.4 (阈值:60)
2025-08-01 04:27:13,543 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=61.4 (阈值:60)
2025-08-01 04:27:13,552 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(52,249), 尺寸39x14, 长宽比2.79, 面积546
2025-08-01 04:27:13,552 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(52,249), 尺寸39x14, 长宽比2.79, 面积546
2025-08-01 04:27:13,572 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(51,249), 尺寸25x12, 长宽比2.08, 面积300
2025-08-01 04:27:13,572 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(51,249), 尺寸25x12, 长宽比2.08, 面积300
2025-08-01 04:27:13,600 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(48,249), 尺寸13x7, 长宽比1.86, 面积91
2025-08-01 04:27:13,600 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(48,249), 尺寸13x7, 长宽比1.86, 面积91
2025-08-01 04:27:13,604 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,240), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 04:27:13,604 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,240), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 04:27:13,610 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,240), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 04:27:13,610 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,240), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 04:27:13,636 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(170,238), 尺寸3x5, 长宽比0.60, 面积15
2025-08-01 04:27:13,636 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(170,238), 尺寸3x5, 长宽比0.60, 面积15
2025-08-01 04:27:13,637 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(156,238), 尺寸3x5, 长宽比0.60, 面积15
2025-08-01 04:27:13,637 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(156,238), 尺寸3x5, 长宽比0.60, 面积15
2025-08-01 04:27:13,639 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,237), 尺寸3x4, 长宽比0.75, 面积12
2025-08-01 04:27:13,639 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,237), 尺寸3x4, 长宽比0.75, 面积12
2025-08-01 04:27:13,640 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,237), 尺寸9x6, 长宽比1.50, 面积54
2025-08-01 04:27:13,640 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,237), 尺寸9x6, 长宽比1.50, 面积54
2025-08-01 04:27:13,642 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(144,237), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 04:27:13,642 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(144,237), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 04:27:13,657 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,235), 尺寸8x8, 长宽比1.00, 面积64
2025-08-01 04:27:13,657 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,235), 尺寸8x8, 长宽比1.00, 面积64
2025-08-01 04:27:13,668 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(144,235), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 04:27:13,668 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(144,235), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 04:27:13,670 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,235), 尺寸3x4, 长宽比0.75, 面积12
2025-08-01 04:27:13,670 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,235), 尺寸3x4, 长宽比0.75, 面积12
2025-08-01 04:27:13,671 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(127,235), 尺寸10x7, 长宽比1.43, 面积70
2025-08-01 04:27:13,671 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(127,235), 尺寸10x7, 长宽比1.43, 面积70
2025-08-01 04:27:13,672 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(133,234), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 04:27:13,672 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(133,234), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 04:27:13,676 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(129,234), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 04:27:13,676 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(129,234), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 04:27:13,686 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(136,231), 尺寸12x3, 长宽比4.00, 面积36
2025-08-01 04:27:13,686 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(136,231), 尺寸12x3, 长宽比4.00, 面积36
2025-08-01 04:27:13,690 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,231), 尺寸1x2, 长宽比0.50, 面积2
2025-08-01 04:27:13,690 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,231), 尺寸1x2, 长宽比0.50, 面积2
2025-08-01 04:27:13,701 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,231), 尺寸28x13, 长宽比2.15, 面积364
2025-08-01 04:27:13,701 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,231), 尺寸28x13, 长宽比2.15, 面积364
2025-08-01 04:27:13,703 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=90.5 (阈值:60)
2025-08-01 04:27:13,703 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=90.5 (阈值:60)
2025-08-01 04:27:13,704 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,230), 尺寸45x14, 长宽比3.21, 面积630
2025-08-01 04:27:13,704 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,230), 尺寸45x14, 长宽比3.21, 面积630
2025-08-01 04:27:13,708 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=82.0 (阈值:60)
2025-08-01 04:27:13,708 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=82.0 (阈值:60)
2025-08-01 04:27:13,710 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 04:27:13,710 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 04:27:13,723 - WeChatAutoAdd - INFO - 底部区域找到 3 个按钮候选
2025-08-01 04:27:13,723 - WeChatAutoAdd - INFO - 底部区域找到 3 个按钮候选
2025-08-01 04:27:13,735 - WeChatAutoAdd - INFO - 选择已知按钮特征: Y=323, 很可能是'添加到通讯录'按钮
2025-08-01 04:27:13,735 - WeChatAutoAdd - INFO - 选择已知按钮特征: Y=323, 很可能是'添加到通讯录'按钮
2025-08-01 04:27:13,737 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 338), 尺寸: 128x30, 位置得分: 5.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-08-01 04:27:13,737 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 338), 尺寸: 128x30, 位置得分: 5.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-08-01 04:27:13,739 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-08-01 04:27:13,739 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-08-01 04:27:13,753 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_042713.png
2025-08-01 04:27:13,753 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_042713.png
2025-08-01 04:27:13,768 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-08-01 04:27:13,768 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-08-01 04:27:13,769 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 04:27:13,769 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 04:27:14,071 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 338) -> 屏幕坐标(1364, 338)
2025-08-01 04:27:14,071 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 338) -> 屏幕坐标(1364, 338)
2025-08-01 04:27:14,884 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 04:27:14,884 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 04:27:14,899 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 04:27:14,899 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 04:27:14,901 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 04:27:14,901 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 04:27:14,903 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 04:27:14,903 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 04:27:14,904 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 04:27:14,904 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 04:27:14,932 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 04:27:14,932 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 04:27:15,499 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 04:27:15,499 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 04:27:15,501 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 2 个
2025-08-01 04:27:15,501 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 2 个
2025-08-01 04:27:15,502 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-08-01 04:27:15,502 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-08-01 04:27:15,503 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 04:27:15,503 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 04:27:15,504 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 04:27:15,504 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 04:27:15,504 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 04:27:15,504 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 04:27:16,024 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:27:16,024 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:27:16,391 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:27:16,391 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:27:16,706 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:27:16,706 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:27:17,149 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:27:17,149 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:27:17,388 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:27:17,388 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:27:17,889 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:27:17,889 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:27:18,282 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:27:18,282 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:27:18,529 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:27:18,529 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:27:18,748 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 04:27:18,748 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 04:27:18,749 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 04:27:18,749 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 04:27:18,750 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 04:27:18,750 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 04:27:56,758 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 04:27:56,758 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 04:27:56,758 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 04:27:56,762 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-08-01 04:27:56,762 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-08-01 04:27:56,762 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-08-01 04:27:56,765 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 04:27:56,765 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 04:27:56,765 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 04:27:56,772 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 04:27:56,772 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 04:27:56,772 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 04:27:56,778 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 04:27:56,778 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 04:27:56,778 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 04:27:56,781 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 04:27:56,781 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 04:27:56,781 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 04:27:56,793 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 04:27:56,793 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 04:27:56,793 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 04:27:56,798 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 04:27:56,798 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 04:27:56,798 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 04:27:56,801 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-08-01 04:27:56,801 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-08-01 04:27:56,801 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-08-01 04:27:56,802 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 04:27:56,802 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 04:27:56,802 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 04:27:57,304 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 04:27:57,304 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 04:27:57,304 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 04:27:57,305 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 04:27:57,305 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 04:27:57,305 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 04:27:57,386 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差34.87, 边缘比例0.0374
2025-08-01 04:27:57,386 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差34.87, 边缘比例0.0374
2025-08-01 04:27:57,386 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差34.87, 边缘比例0.0374
2025-08-01 04:27:57,404 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_042757.png
2025-08-01 04:27:57,404 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_042757.png
2025-08-01 04:27:57,404 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_042757.png
2025-08-01 04:27:57,407 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 04:27:57,407 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 04:27:57,407 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 04:27:57,420 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 04:27:57,420 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 04:27:57,420 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 04:27:57,432 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 04:27:57,432 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 04:27:57,432 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 04:27:57,433 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 04:27:57,433 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 04:27:57,433 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 04:27:57,434 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 04:27:57,434 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 04:27:57,434 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 04:27:57,438 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_042757.png
2025-08-01 04:27:57,438 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_042757.png
2025-08-01 04:27:57,438 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_042757.png
2025-08-01 04:27:57,440 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 04:27:57,440 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 04:27:57,440 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 04:27:57,453 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 04:27:57,453 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 04:27:57,453 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 04:27:57,455 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 04:27:57,455 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 04:27:57,455 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 04:27:57,465 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 04:27:57,465 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 04:27:57,465 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 04:27:57,467 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 04:27:57,467 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 04:27:57,467 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 04:27:57,470 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 04:27:57,470 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 04:27:57,470 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 04:27:57,471 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 04:27:57,471 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 04:27:57,471 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 04:27:57,472 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 04:27:57,472 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 04:27:57,472 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 04:27:57,473 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 04:27:57,473 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 04:27:57,473 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 04:27:57,482 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 04:27:57,482 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 04:27:57,482 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 04:27:57,506 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_042757.png
2025-08-01 04:27:57,506 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_042757.png
2025-08-01 04:27:57,506 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_042757.png
2025-08-01 04:27:57,532 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 04:27:57,532 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 04:27:57,532 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 04:27:57,534 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 04:27:57,534 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 04:27:57,534 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 04:27:57,539 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_042757.png
2025-08-01 04:27:57,539 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_042757.png
2025-08-01 04:27:57,539 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_042757.png
2025-08-01 04:27:57,706 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 04:27:57,706 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 04:27:57,706 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 04:27:57,709 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 04:27:57,709 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 04:27:57,709 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 04:27:57,737 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 04:27:57,737 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 04:27:57,737 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 04:27:57,740 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 04:27:57,740 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 04:27:57,740 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 04:27:58,066 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 04:27:58,066 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 04:27:58,066 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 04:27:58,838 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 04:27:58,838 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 04:27:58,838 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 04:27:58,839 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 04:27:58,839 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 04:27:58,839 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 04:27:58,840 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 04:27:58,840 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 04:27:58,840 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 04:27:58,846 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 04:27:58,846 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 04:27:58,846 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 04:27:58,849 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 04:27:58,849 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 04:27:58,849 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 04:27:58,851 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 04:27:58,851 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 04:27:58,851 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 04:27:58,856 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 04:27:58,856 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 04:27:58,856 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 04:27:58,865 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 2 个
2025-08-01 04:27:58,865 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 2 个
2025-08-01 04:27:58,865 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 2 个
2025-08-01 04:27:58,866 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-08-01 04:27:58,866 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-08-01 04:27:58,866 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-08-01 04:27:58,869 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 04:27:58,869 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 04:27:58,869 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 04:27:58,871 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 04:27:58,871 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 04:27:58,871 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 04:27:58,872 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 04:27:58,872 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 04:27:58,872 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 04:27:59,454 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:27:59,454 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:27:59,454 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:27:59,697 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:27:59,697 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:27:59,697 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:28:00,011 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:28:00,011 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:28:00,011 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:28:00,253 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:28:00,253 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:28:00,253 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:28:00,497 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:28:00,497 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:28:00,497 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:28:00,795 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:28:00,795 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:28:00,795 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:28:01,036 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:28:01,036 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:28:01,036 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:28:01,349 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:28:01,349 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:28:01,349 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:28:01,591 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:28:01,591 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:28:01,591 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 04:28:01,896 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 04:28:01,896 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 04:28:01,896 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 04:28:01,897 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 04:28:01,897 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 04:28:01,897 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 04:28:01,898 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 04:28:01,898 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 04:28:01,898 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 04:38:35,712 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 12:28:36,103 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 12:29:07,460 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 12:29:07,460 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 12:29:07,464 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 12:29:07,464 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 12:29:07,466 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 12:29:07,466 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 12:29:07,470 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 12:29:07,470 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 12:29:07,470 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 12:29:07,470 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 12:29:07,471 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 12:29:07,471 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 12:29:07,473 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 12:29:07,473 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 12:29:07,474 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 12:29:07,474 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 12:29:07,475 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 12:29:07,475 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 12:29:07,976 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 12:29:07,976 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 12:29:07,977 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 12:29:07,977 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 12:29:08,055 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-08-01 12:29:08,055 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-08-01 12:29:08,056 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-08-01 12:29:08,056 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-08-01 12:29:08,062 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_122908.png
2025-08-01 12:29:08,062 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_122908.png
2025-08-01 12:29:08,063 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 12:29:08,063 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 12:29:08,064 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 12:29:08,064 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 12:29:08,065 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 12:29:08,065 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 12:29:08,066 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 12:29:08,066 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 12:29:08,067 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 12:29:08,067 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 12:29:08,071 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_122908.png
2025-08-01 12:29:08,071 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_122908.png
2025-08-01 12:29:08,074 - WeChatAutoAdd - INFO - 底部区域原始检测到 3 个轮廓
2025-08-01 12:29:08,074 - WeChatAutoAdd - INFO - 底部区域原始检测到 3 个轮廓
2025-08-01 12:29:08,074 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 12:29:08,074 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 12:29:08,075 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 12:29:08,075 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 12:29:08,077 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 12:29:08,077 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 12:29:08,078 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 12:29:08,078 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 12:29:08,078 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 12:29:08,078 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 12:29:08,080 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 12:29:08,080 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 12:29:08,081 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 12:29:08,081 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 12:29:08,081 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 12:29:08,081 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 12:29:08,087 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_122908.png
2025-08-01 12:29:08,087 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_122908.png
2025-08-01 12:29:08,088 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 12:29:08,088 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 12:29:08,089 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 12:29:08,089 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 12:29:08,092 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_122908.png
2025-08-01 12:29:08,092 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_122908.png
2025-08-01 12:29:08,191 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 12:29:08,191 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 12:29:08,192 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 12:29:08,192 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 12:29:08,193 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 12:29:08,193 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 12:29:08,193 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 12:29:08,193 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 12:29:08,495 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 12:29:08,495 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 12:29:09,281 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 12:29:09,281 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 12:29:09,282 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 12:29:09,282 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 12:29:09,283 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 12:29:09,283 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 12:29:09,284 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 12:29:09,284 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 12:29:09,284 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 12:29:09,284 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 12:29:09,286 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 12:29:09,286 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 12:29:09,288 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 12:29:09,288 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 12:29:09,290 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 2 个
2025-08-01 12:29:09,290 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 2 个
2025-08-01 12:29:09,291 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-08-01 12:29:09,291 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-08-01 12:29:09,292 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 12:29:09,292 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 12:29:09,293 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 12:29:09,293 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 12:29:09,295 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 12:29:09,295 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 12:29:09,819 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:09,819 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:10,082 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:10,082 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:10,320 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:10,320 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:10,558 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:10,558 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:10,798 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:10,798 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:11,037 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:11,037 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:11,321 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:11,321 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:11,561 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:11,561 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:11,800 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:11,800 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:12,037 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:12,037 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:12,277 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:12,277 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:12,498 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 12:29:12,498 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 12:29:12,499 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 12:29:12,499 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 12:29:12,499 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 12:29:12,499 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 12:29:49,740 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 12:29:49,740 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 12:29:49,740 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 12:29:49,742 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 12:29:49,742 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 12:29:49,742 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 12:29:49,744 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 12:29:49,744 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 12:29:49,744 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 12:29:49,747 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 12:29:49,747 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 12:29:49,747 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 12:29:49,748 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 12:29:49,748 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 12:29:49,748 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 12:29:49,749 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 12:29:49,749 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 12:29:49,749 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 12:29:49,751 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 12:29:49,751 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 12:29:49,751 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 12:29:49,752 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 12:29:49,752 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 12:29:49,752 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 12:29:49,753 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 12:29:49,753 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 12:29:49,753 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 12:29:50,255 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 12:29:50,255 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 12:29:50,255 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 12:29:50,256 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 12:29:50,256 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 12:29:50,256 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 12:29:50,306 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差25.74, 边缘比例0.0352
2025-08-01 12:29:50,306 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差25.74, 边缘比例0.0352
2025-08-01 12:29:50,306 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差25.74, 边缘比例0.0352
2025-08-01 12:29:50,311 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_122950.png
2025-08-01 12:29:50,311 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_122950.png
2025-08-01 12:29:50,311 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_122950.png
2025-08-01 12:29:50,312 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 12:29:50,312 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 12:29:50,312 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 12:29:50,313 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 12:29:50,313 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 12:29:50,313 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 12:29:50,314 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 12:29:50,314 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 12:29:50,314 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 12:29:50,315 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 12:29:50,315 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 12:29:50,315 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 12:29:50,316 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 12:29:50,316 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 12:29:50,316 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 12:29:50,319 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_122950.png
2025-08-01 12:29:50,319 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_122950.png
2025-08-01 12:29:50,319 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_122950.png
2025-08-01 12:29:50,320 - WeChatAutoAdd - INFO - 底部区域原始检测到 39 个轮廓
2025-08-01 12:29:50,320 - WeChatAutoAdd - INFO - 底部区域原始检测到 39 个轮廓
2025-08-01 12:29:50,320 - WeChatAutoAdd - INFO - 底部区域原始检测到 39 个轮廓
2025-08-01 12:29:50,321 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 12:29:50,321 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 12:29:50,321 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 12:29:50,322 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 12:29:50,322 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 12:29:50,322 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 12:29:50,323 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 12:29:50,323 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 12:29:50,323 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 12:29:50,325 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 12:29:50,325 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 12:29:50,325 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 12:29:50,326 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-08-01 12:29:50,326 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-08-01 12:29:50,326 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-08-01 12:29:50,327 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 12:29:50,327 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 12:29:50,327 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 12:29:50,328 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-08-01 12:29:50,328 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-08-01 12:29:50,328 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-08-01 12:29:50,330 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-08-01 12:29:50,330 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-08-01 12:29:50,330 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-08-01 12:29:50,332 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 12:29:50,332 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 12:29:50,332 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 12:29:50,333 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-08-01 12:29:50,333 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-08-01 12:29:50,333 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-08-01 12:29:50,334 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-08-01 12:29:50,334 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-08-01 12:29:50,334 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-08-01 12:29:50,335 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-08-01 12:29:50,335 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-08-01 12:29:50,335 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-08-01 12:29:50,337 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 12:29:50,337 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 12:29:50,337 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 12:29:50,338 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 12:29:50,338 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 12:29:50,338 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 12:29:50,338 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 12:29:50,338 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 12:29:50,338 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 12:29:50,339 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 12:29:50,339 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 12:29:50,339 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 12:29:50,341 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-08-01 12:29:50,341 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-08-01 12:29:50,341 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-08-01 12:29:50,343 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-08-01 12:29:50,343 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-08-01 12:29:50,343 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-08-01 12:29:50,344 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 12:29:50,344 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 12:29:50,344 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 12:29:50,345 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 12:29:50,345 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 12:29:50,345 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 12:29:50,346 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-08-01 12:29:50,346 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-08-01 12:29:50,346 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-08-01 12:29:50,347 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-08-01 12:29:50,347 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-08-01 12:29:50,347 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-08-01 12:29:50,348 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-08-01 12:29:50,348 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-08-01 12:29:50,348 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-08-01 12:29:50,350 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 12:29:50,350 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 12:29:50,350 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 12:29:50,351 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-08-01 12:29:50,351 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-08-01 12:29:50,351 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-08-01 12:29:50,352 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-08-01 12:29:50,352 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-08-01 12:29:50,352 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-08-01 12:29:50,353 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-08-01 12:29:50,353 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-08-01 12:29:50,353 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-08-01 12:29:50,354 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-08-01 12:29:50,354 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-08-01 12:29:50,354 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-08-01 12:29:50,356 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-08-01 12:29:50,356 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-08-01 12:29:50,356 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-08-01 12:29:50,358 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-08-01 12:29:50,358 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-08-01 12:29:50,358 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-08-01 12:29:50,359 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-08-01 12:29:50,359 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-08-01 12:29:50,359 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-08-01 12:29:50,361 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-08-01 12:29:50,361 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-08-01 12:29:50,361 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-08-01 12:29:50,362 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-08-01 12:29:50,362 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-08-01 12:29:50,362 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-08-01 12:29:50,363 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-08-01 12:29:50,363 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-08-01 12:29:50,363 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-08-01 12:29:50,364 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-08-01 12:29:50,364 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-08-01 12:29:50,364 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-08-01 12:29:50,366 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-08-01 12:29:50,366 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-08-01 12:29:50,366 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-08-01 12:29:50,368 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-08-01 12:29:50,368 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-08-01 12:29:50,368 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-08-01 12:29:50,369 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-08-01 12:29:50,369 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-08-01 12:29:50,369 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-08-01 12:29:50,370 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 12:29:50,370 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 12:29:50,370 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 12:29:50,373 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-08-01 12:29:50,373 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-08-01 12:29:50,373 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-08-01 12:29:50,374 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-08-01 12:29:50,374 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-08-01 12:29:50,374 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-08-01 12:29:50,376 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 12:29:50,376 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 12:29:50,376 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 12:29:50,377 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 12:29:50,377 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 12:29:50,377 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 12:29:50,382 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_122950.png
2025-08-01 12:29:50,382 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_122950.png
2025-08-01 12:29:50,382 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_122950.png
2025-08-01 12:29:50,384 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 12:29:50,384 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 12:29:50,384 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 12:29:50,385 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-08-01 12:29:50,385 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-08-01 12:29:50,385 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-08-01 12:29:50,388 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_122950.png
2025-08-01 12:29:50,388 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_122950.png
2025-08-01 12:29:50,388 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_122950.png
2025-08-01 12:29:50,414 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 12:29:50,414 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 12:29:50,414 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 12:29:50,417 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-08-01 12:29:50,417 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-08-01 12:29:50,417 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-08-01 12:29:50,419 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 12:29:50,419 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 12:29:50,419 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 12:29:50,423 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 12:29:50,423 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 12:29:50,423 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 12:29:50,726 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-08-01 12:29:50,726 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-08-01 12:29:50,726 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-08-01 12:29:51,494 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 12:29:51,494 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 12:29:51,494 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 12:29:51,495 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 12:29:51,495 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 12:29:51,495 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 12:29:51,496 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 12:29:51,496 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 12:29:51,496 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 12:29:51,497 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 12:29:51,497 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 12:29:51,497 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 12:29:51,499 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 12:29:51,499 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 12:29:51,499 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 12:29:51,500 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 12:29:51,500 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 12:29:51,500 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 12:29:51,501 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 12:29:51,501 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 12:29:51,501 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 12:29:51,502 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 2 个
2025-08-01 12:29:51,502 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 2 个
2025-08-01 12:29:51,502 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 2 个
2025-08-01 12:29:51,503 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-08-01 12:29:51,503 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-08-01 12:29:51,503 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-08-01 12:29:51,504 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 12:29:51,504 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 12:29:51,504 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 12:29:51,504 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 12:29:51,504 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 12:29:51,504 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 12:29:51,505 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 12:29:51,505 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 12:29:51,505 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 12:29:52,029 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:52,029 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:52,029 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:52,267 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:52,267 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:52,267 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:52,504 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:52,504 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:52,504 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:52,745 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:52,745 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:52,745 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:52,997 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:52,997 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:52,997 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:53,237 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:53,237 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:53,237 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:53,518 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:53,518 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:53,518 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:53,756 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:53,756 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:53,756 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:54,040 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:54,040 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:54,040 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:54,280 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:54,280 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:54,280 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:29:54,543 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 12:29:54,543 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 12:29:54,543 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 12:29:54,544 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 12:29:54,544 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 12:29:54,544 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 12:29:54,545 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 12:29:54,545 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 12:29:54,545 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 12:55:14,318 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 12:55:45,995 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 12:55:45,995 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 12:55:45,998 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 12:55:45,998 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 12:55:45,999 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 12:55:45,999 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 12:55:46,002 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 12:55:46,002 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 12:55:46,003 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 12:55:46,003 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 12:55:46,123 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 12:55:46,123 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 12:55:46,124 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 12:55:46,124 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 12:55:46,125 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 12:55:46,125 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 12:55:46,126 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-08-01 12:55:46,126 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-08-01 12:55:46,126 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 12:55:46,126 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 12:55:46,627 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 12:55:46,627 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 12:55:46,628 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 12:55:46,628 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 12:55:46,706 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-08-01 12:55:46,706 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-08-01 12:55:46,707 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-08-01 12:55:46,707 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-08-01 12:55:46,714 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_125546.png
2025-08-01 12:55:46,714 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_125546.png
2025-08-01 12:55:46,717 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 12:55:46,717 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 12:55:46,718 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 12:55:46,718 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 12:55:46,720 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 12:55:46,720 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 12:55:46,721 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 12:55:46,721 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 12:55:46,722 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 12:55:46,722 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 12:55:46,727 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_125546.png
2025-08-01 12:55:46,727 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_125546.png
2025-08-01 12:55:46,728 - WeChatAutoAdd - INFO - 底部区域原始检测到 3 个轮廓
2025-08-01 12:55:46,728 - WeChatAutoAdd - INFO - 底部区域原始检测到 3 个轮廓
2025-08-01 12:55:46,730 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 12:55:46,730 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 12:55:46,731 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 12:55:46,731 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 12:55:46,733 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 12:55:46,733 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 12:55:46,734 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 12:55:46,734 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 12:55:46,735 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 12:55:46,735 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 12:55:46,736 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 12:55:46,736 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 12:55:46,742 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 12:55:46,742 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 12:55:46,751 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 12:55:46,751 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 12:55:46,770 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_125546.png
2025-08-01 12:55:46,770 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_125546.png
2025-08-01 12:55:46,777 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 12:55:46,777 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 12:55:46,778 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 12:55:46,778 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 12:55:46,784 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_125546.png
2025-08-01 12:55:46,784 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_125546.png
2025-08-01 12:55:46,859 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 12:55:46,859 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 12:55:46,860 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 12:55:46,860 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 12:55:46,861 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 12:55:46,861 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 12:55:46,861 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 12:55:46,861 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 12:55:47,164 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 12:55:47,164 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 12:55:47,933 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 12:55:47,933 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 12:55:47,934 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 12:55:47,934 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 12:55:47,935 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 12:55:47,935 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 12:55:47,935 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 12:55:47,935 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 12:55:47,936 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 12:55:47,936 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 12:55:47,937 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 12:55:47,937 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 12:55:47,940 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 12:55:47,940 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 12:55:47,942 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 2 个
2025-08-01 12:55:47,942 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 2 个
2025-08-01 12:55:47,943 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-08-01 12:55:47,943 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-08-01 12:55:47,944 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 12:55:47,944 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 12:55:47,945 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 12:55:47,945 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 12:55:47,947 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 12:55:47,947 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 12:55:48,473 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:55:48,473 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:55:48,721 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:55:48,721 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:55:48,962 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:55:48,962 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:55:49,201 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:55:49,201 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:55:49,440 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:55:49,440 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:55:49,784 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:55:49,784 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:55:50,023 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:55:50,023 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:55:50,262 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:55:50,262 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:55:50,500 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:55:50,500 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:55:50,793 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:55:50,793 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 12:55:51,013 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 12:55:51,013 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 12:55:51,013 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 12:55:51,013 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 12:55:51,014 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 12:55:51,014 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:23:57,526 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:24:28,967 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:24:28,967 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:24:28,970 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:24:28,970 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:24:28,971 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:24:28,971 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:24:28,974 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:24:28,974 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:24:28,976 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:24:28,976 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:24:28,979 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:24:28,979 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:24:28,981 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:24:28,981 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:24:28,984 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:24:28,984 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:24:28,985 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:24:28,985 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:24:29,488 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:24:29,488 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:24:29,489 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:24:29,489 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:24:29,562 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-08-01 14:24:29,562 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-08-01 14:24:29,563 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-08-01 14:24:29,563 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-08-01 14:24:29,569 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142429.png
2025-08-01 14:24:29,569 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142429.png
2025-08-01 14:24:29,572 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:24:29,572 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:24:29,577 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:24:29,577 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:24:29,579 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:24:29,579 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:24:29,581 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:24:29,581 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:24:29,582 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:24:29,582 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:24:29,590 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142429.png
2025-08-01 14:24:29,590 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142429.png
2025-08-01 14:24:29,594 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:24:29,594 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:24:29,595 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:24:29,595 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:24:29,596 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:24:29,596 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:24:29,598 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:24:29,598 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:24:29,600 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:24:29,600 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:24:29,606 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:24:29,606 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:24:29,607 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:24:29,607 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:24:29,609 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:24:29,609 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:24:29,611 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:24:29,611 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:24:29,619 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:24:29,619 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:24:29,633 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142429.png
2025-08-01 14:24:29,633 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142429.png
2025-08-01 14:24:29,637 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:24:29,637 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:24:29,644 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:24:29,644 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:24:29,675 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142429.png
2025-08-01 14:24:29,675 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142429.png
2025-08-01 14:24:29,746 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:24:29,746 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:24:29,747 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:24:29,747 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:24:29,748 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:24:29,748 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:24:29,748 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:24:29,748 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:24:30,053 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:24:30,053 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:24:30,838 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:24:30,838 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:24:30,839 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:24:30,839 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:24:30,840 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:24:30,840 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:24:30,840 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:24:30,840 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:24:30,841 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:24:30,841 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:24:30,842 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:24:30,842 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:24:30,845 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:24:30,845 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:24:30,848 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:24:30,848 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:24:30,849 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:24:30,849 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:24:30,849 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:24:30,849 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:24:30,850 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:24:30,850 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:24:31,383 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:24:31,383 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:24:31,670 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:24:31,670 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:24:31,926 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:24:31,926 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:24:32,173 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:24:32,173 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:24:32,418 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:24:32,418 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:24:32,700 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:24:32,700 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:24:32,946 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:24:32,946 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:24:33,247 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:24:33,247 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:24:33,492 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:24:33,492 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:24:33,771 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:24:33,771 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:24:33,993 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:24:33,993 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:24:33,994 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:24:33,994 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:24:33,994 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:24:33,994 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:25:11,165 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:25:11,165 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:25:11,165 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:25:11,169 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:25:11,169 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:25:11,169 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:25:11,172 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:25:11,172 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:25:11,172 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:25:11,178 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:25:11,178 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:25:11,178 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:25:11,184 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:25:11,184 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:25:11,184 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:25:11,188 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:25:11,188 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:25:11,188 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:25:11,191 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:25:11,191 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:25:11,191 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:25:11,193 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:25:11,193 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:25:11,193 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:25:11,195 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:25:11,195 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:25:11,195 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:25:11,698 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:25:11,698 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:25:11,698 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:25:11,699 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:25:11,699 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:25:11,699 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:25:11,766 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-08-01 14:25:11,766 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-08-01 14:25:11,766 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-08-01 14:25:11,767 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-08-01 14:25:11,767 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-08-01 14:25:11,767 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-08-01 14:25:11,772 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142511.png
2025-08-01 14:25:11,772 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142511.png
2025-08-01 14:25:11,772 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142511.png
2025-08-01 14:25:11,775 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:25:11,775 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:25:11,775 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:25:11,780 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:25:11,780 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:25:11,780 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:25:11,792 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:25:11,792 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:25:11,792 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:25:11,794 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:25:11,794 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:25:11,794 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:25:11,795 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:25:11,795 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:25:11,795 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:25:11,798 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142511.png
2025-08-01 14:25:11,798 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142511.png
2025-08-01 14:25:11,798 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142511.png
2025-08-01 14:25:11,804 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:25:11,804 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:25:11,804 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:25:11,805 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:25:11,805 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:25:11,805 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:25:11,807 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:25:11,807 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:25:11,807 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:25:11,808 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:25:11,808 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:25:11,808 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:25:11,810 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:25:11,810 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:25:11,810 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:25:11,811 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:25:11,811 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:25:11,811 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:25:11,813 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:25:11,813 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:25:11,813 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:25:11,814 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:25:11,814 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:25:11,814 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:25:11,815 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:25:11,815 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:25:11,815 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:25:11,820 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:25:11,820 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:25:11,820 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:25:11,838 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142511.png
2025-08-01 14:25:11,838 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142511.png
2025-08-01 14:25:11,838 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142511.png
2025-08-01 14:25:11,843 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:25:11,843 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:25:11,843 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:25:11,845 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:25:11,845 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:25:11,845 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:25:11,847 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142511.png
2025-08-01 14:25:11,847 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142511.png
2025-08-01 14:25:11,847 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142511.png
2025-08-01 14:25:11,899 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:25:11,899 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:25:11,899 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:25:11,902 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:25:11,902 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:25:11,902 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:25:11,905 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:25:11,905 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:25:11,905 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:25:11,907 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:25:11,907 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:25:11,907 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:25:12,209 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:25:12,209 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:25:12,209 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:25:13,023 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:25:13,023 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:25:13,023 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:25:13,024 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:25:13,024 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:25:13,024 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:25:13,025 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:25:13,025 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:25:13,025 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:25:13,026 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:25:13,026 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:25:13,026 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:25:13,027 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:25:13,027 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:25:13,027 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:25:13,028 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:25:13,028 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:25:13,028 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:25:13,031 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:25:13,031 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:25:13,031 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:25:13,041 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:25:13,041 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:25:13,041 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:25:13,042 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:25:13,042 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:25:13,042 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:25:13,046 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:25:13,046 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:25:13,046 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:25:13,055 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:25:13,055 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:25:13,055 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:25:13,586 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:13,586 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:13,586 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:13,865 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:13,865 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:13,865 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:14,111 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:14,111 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:14,111 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:14,407 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:14,407 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:14,407 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:14,655 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:14,655 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:14,655 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:14,901 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:14,901 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:14,901 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:15,226 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:15,226 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:15,226 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:15,474 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:15,474 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:15,474 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:15,724 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:15,724 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:15,724 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:15,972 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:15,972 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:15,972 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:25:16,276 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:25:16,276 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:25:16,276 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:25:16,277 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:25:16,277 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:25:16,277 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:25:16,278 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:25:16,278 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:25:16,278 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:26:15,594 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:26:15,594 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:26:15,594 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:26:15,594 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:26:15,597 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:26:15,597 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:26:15,597 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:26:15,597 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:26:15,598 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:26:15,598 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:26:15,598 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:26:15,598 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:26:15,601 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:26:15,601 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:26:15,601 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:26:15,601 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:26:15,603 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:26:15,603 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:26:15,603 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:26:15,603 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:26:15,612 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:26:15,612 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:26:15,612 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:26:15,612 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:26:15,614 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:26:15,614 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:26:15,614 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:26:15,614 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:26:15,615 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:26:15,615 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:26:15,615 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:26:15,615 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:26:15,617 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:26:15,617 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:26:15,617 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:26:15,617 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:26:16,122 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:26:16,122 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:26:16,122 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:26:16,122 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:26:16,124 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:26:16,124 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:26:16,124 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:26:16,124 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:26:16,179 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差38.94, 边缘比例0.0360
2025-08-01 14:26:16,179 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差38.94, 边缘比例0.0360
2025-08-01 14:26:16,179 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差38.94, 边缘比例0.0360
2025-08-01 14:26:16,179 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差38.94, 边缘比例0.0360
2025-08-01 14:26:16,186 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142616.png
2025-08-01 14:26:16,186 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142616.png
2025-08-01 14:26:16,186 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142616.png
2025-08-01 14:26:16,186 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142616.png
2025-08-01 14:26:16,187 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:26:16,187 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:26:16,187 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:26:16,187 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:26:16,189 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:26:16,189 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:26:16,189 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:26:16,189 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:26:16,191 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:26:16,191 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:26:16,191 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:26:16,191 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:26:16,192 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:26:16,192 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:26:16,192 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:26:16,192 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:26:16,206 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:26:16,206 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:26:16,206 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:26:16,206 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:26:16,221 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142616.png
2025-08-01 14:26:16,221 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142616.png
2025-08-01 14:26:16,221 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142616.png
2025-08-01 14:26:16,221 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142616.png
2025-08-01 14:26:16,226 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:26:16,226 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:26:16,226 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:26:16,226 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:26:16,227 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:26:16,227 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:26:16,227 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:26:16,227 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:26:16,233 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:26:16,233 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:26:16,233 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:26:16,233 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:26:16,234 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:26:16,234 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:26:16,234 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:26:16,234 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:26:16,236 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:26:16,236 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:26:16,236 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:26:16,236 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:26:16,240 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:26:16,240 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:26:16,240 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:26:16,240 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:26:16,241 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:26:16,241 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:26:16,241 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:26:16,241 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:26:16,242 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:26:16,242 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:26:16,242 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:26:16,242 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:26:16,243 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:26:16,243 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:26:16,243 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:26:16,243 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:26:16,246 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:26:16,246 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:26:16,246 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:26:16,246 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:26:16,257 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142616.png
2025-08-01 14:26:16,257 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142616.png
2025-08-01 14:26:16,257 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142616.png
2025-08-01 14:26:16,257 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142616.png
2025-08-01 14:26:16,260 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:26:16,260 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:26:16,260 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:26:16,260 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:26:16,262 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:26:16,262 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:26:16,262 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:26:16,262 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:26:16,272 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142616.png
2025-08-01 14:26:16,272 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142616.png
2025-08-01 14:26:16,272 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142616.png
2025-08-01 14:26:16,272 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142616.png
2025-08-01 14:26:16,305 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:26:16,305 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:26:16,305 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:26:16,305 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:26:16,307 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:26:16,307 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:26:16,307 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:26:16,307 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:26:16,309 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:26:16,309 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:26:16,309 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:26:16,309 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:26:16,310 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:26:16,310 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:26:16,310 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:26:16,310 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:26:16,614 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:26:16,614 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:26:16,614 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:26:16,614 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:26:17,399 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:26:17,399 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:26:17,399 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:26:17,399 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:26:17,400 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:26:17,400 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:26:17,400 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:26:17,400 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:26:17,401 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:26:17,401 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:26:17,401 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:26:17,401 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:26:17,402 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:26:17,402 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:26:17,402 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:26:17,402 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:26:17,403 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:26:17,403 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:26:17,403 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:26:17,403 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:26:17,406 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:26:17,406 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:26:17,406 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:26:17,406 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:26:17,409 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:26:17,409 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:26:17,409 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:26:17,409 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:26:17,413 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:26:17,413 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:26:17,413 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:26:17,413 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:26:17,414 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:26:17,414 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:26:17,414 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:26:17,414 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:26:17,416 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:26:17,416 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:26:17,416 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:26:17,416 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:26:17,417 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:26:17,417 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:26:17,417 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:26:17,417 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:26:17,951 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:17,951 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:17,951 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:17,951 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:18,213 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:18,213 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:18,213 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:18,213 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:18,459 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:18,459 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:18,459 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:18,459 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:18,708 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:18,708 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:18,708 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:18,708 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:18,950 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:18,950 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:18,950 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:18,950 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:19,281 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:19,281 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:19,281 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:19,281 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:19,527 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:19,527 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:19,527 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:19,527 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:19,778 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:19,778 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:19,778 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:19,778 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:20,025 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:20,025 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:20,025 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:20,025 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:20,267 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:20,267 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:20,267 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:20,267 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:26:20,572 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:26:20,572 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:26:20,572 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:26:20,572 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:26:20,574 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:26:20,574 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:26:20,574 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:26:20,574 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:26:20,576 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:26:20,576 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:26:20,576 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:26:20,576 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:26:57,874 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:26:57,874 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:26:57,874 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:26:57,874 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:26:57,874 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:26:57,878 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:26:57,878 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:26:57,878 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:26:57,878 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:26:57,878 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:26:57,880 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:26:57,880 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:26:57,880 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:26:57,880 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:26:57,880 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:26:57,886 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:26:57,886 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:26:57,886 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:26:57,886 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:26:57,886 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:26:57,890 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:26:57,890 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:26:57,890 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:26:57,890 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:26:57,890 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:26:57,894 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:26:57,894 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:26:57,894 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:26:57,894 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:26:57,894 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:26:57,897 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:26:57,897 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:26:57,897 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:26:57,897 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:26:57,897 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:26:57,899 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:26:57,899 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:26:57,899 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:26:57,899 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:26:57,899 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:26:57,900 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:26:57,900 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:26:57,900 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:26:57,900 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:26:57,900 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:26:58,406 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:26:58,406 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:26:58,406 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:26:58,406 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:26:58,406 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:26:58,407 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:26:58,407 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:26:58,407 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:26:58,407 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:26:58,407 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:26:58,479 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差25.88, 边缘比例0.0350
2025-08-01 14:26:58,479 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差25.88, 边缘比例0.0350
2025-08-01 14:26:58,479 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差25.88, 边缘比例0.0350
2025-08-01 14:26:58,479 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差25.88, 边缘比例0.0350
2025-08-01 14:26:58,479 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差25.88, 边缘比例0.0350
2025-08-01 14:26:58,483 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142658.png
2025-08-01 14:26:58,483 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142658.png
2025-08-01 14:26:58,483 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142658.png
2025-08-01 14:26:58,483 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142658.png
2025-08-01 14:26:58,483 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142658.png
2025-08-01 14:26:58,485 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:26:58,485 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:26:58,485 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:26:58,485 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:26:58,485 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:26:58,488 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:26:58,488 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:26:58,488 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:26:58,488 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:26:58,488 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:26:58,493 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:26:58,493 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:26:58,493 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:26:58,493 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:26:58,493 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:26:58,494 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:26:58,494 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:26:58,494 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:26:58,494 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:26:58,494 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:26:58,497 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:26:58,497 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:26:58,497 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:26:58,497 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:26:58,497 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:26:58,500 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142658.png
2025-08-01 14:26:58,500 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142658.png
2025-08-01 14:26:58,500 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142658.png
2025-08-01 14:26:58,500 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142658.png
2025-08-01 14:26:58,500 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142658.png
2025-08-01 14:26:58,503 - WeChatAutoAdd - INFO - 底部区域原始检测到 40 个轮廓
2025-08-01 14:26:58,503 - WeChatAutoAdd - INFO - 底部区域原始检测到 40 个轮廓
2025-08-01 14:26:58,503 - WeChatAutoAdd - INFO - 底部区域原始检测到 40 个轮廓
2025-08-01 14:26:58,503 - WeChatAutoAdd - INFO - 底部区域原始检测到 40 个轮廓
2025-08-01 14:26:58,503 - WeChatAutoAdd - INFO - 底部区域原始检测到 40 个轮廓
2025-08-01 14:26:58,504 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:26:58,504 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:26:58,504 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:26:58,504 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:26:58,504 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:26:58,506 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:26:58,506 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:26:58,506 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:26:58,506 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:26:58,506 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:26:58,508 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:26:58,508 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:26:58,508 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:26:58,508 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:26:58,508 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:26:58,516 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:26:58,516 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:26:58,516 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:26:58,516 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:26:58,516 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:26:58,518 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,518 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,518 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,518 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,518 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,519 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-08-01 14:26:58,519 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-08-01 14:26:58,519 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-08-01 14:26:58,519 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-08-01 14:26:58,519 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-08-01 14:26:58,522 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:26:58,522 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:26:58,522 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:26:58,522 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:26:58,522 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:26:58,523 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-08-01 14:26:58,523 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-08-01 14:26:58,523 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-08-01 14:26:58,523 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-08-01 14:26:58,523 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-08-01 14:26:58,527 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-08-01 14:26:58,527 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-08-01 14:26:58,527 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-08-01 14:26:58,527 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-08-01 14:26:58,527 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-08-01 14:26:58,534 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,534 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,534 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,534 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,534 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,535 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-08-01 14:26:58,535 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-08-01 14:26:58,535 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-08-01 14:26:58,535 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-08-01 14:26:58,535 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-08-01 14:26:58,537 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-08-01 14:26:58,537 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-08-01 14:26:58,537 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-08-01 14:26:58,537 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-08-01 14:26:58,537 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-08-01 14:26:58,538 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-08-01 14:26:58,538 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-08-01 14:26:58,538 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-08-01 14:26:58,538 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-08-01 14:26:58,538 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-08-01 14:26:58,544 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,544 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,544 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,544 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,544 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,549 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:26:58,549 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:26:58,549 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:26:58,549 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:26:58,549 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:26:58,550 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:26:58,550 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:26:58,550 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:26:58,550 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:26:58,550 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:26:58,553 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,553 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,553 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,553 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,553 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,555 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-08-01 14:26:58,555 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-08-01 14:26:58,555 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-08-01 14:26:58,555 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-08-01 14:26:58,555 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-08-01 14:26:58,556 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-08-01 14:26:58,556 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-08-01 14:26:58,556 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-08-01 14:26:58,556 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-08-01 14:26:58,556 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-08-01 14:26:58,565 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,565 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,565 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,565 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,565 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,568 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,568 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,568 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,568 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,568 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,570 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-08-01 14:26:58,570 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-08-01 14:26:58,570 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-08-01 14:26:58,570 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-08-01 14:26:58,570 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-08-01 14:26:58,571 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-08-01 14:26:58,571 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-08-01 14:26:58,571 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-08-01 14:26:58,571 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-08-01 14:26:58,571 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-08-01 14:26:58,576 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-08-01 14:26:58,576 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-08-01 14:26:58,576 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-08-01 14:26:58,576 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-08-01 14:26:58,576 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-08-01 14:26:58,585 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,585 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,585 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,585 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,585 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:26:58,586 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-08-01 14:26:58,586 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-08-01 14:26:58,586 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-08-01 14:26:58,586 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-08-01 14:26:58,586 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-08-01 14:26:58,590 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-08-01 14:26:58,590 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-08-01 14:26:58,590 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-08-01 14:26:58,590 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-08-01 14:26:58,590 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-08-01 14:26:58,592 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-08-01 14:26:58,592 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-08-01 14:26:58,592 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-08-01 14:26:58,592 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-08-01 14:26:58,592 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-08-01 14:26:58,600 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-08-01 14:26:58,600 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-08-01 14:26:58,600 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-08-01 14:26:58,600 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-08-01 14:26:58,600 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-08-01 14:26:58,602 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-08-01 14:26:58,602 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-08-01 14:26:58,602 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-08-01 14:26:58,602 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-08-01 14:26:58,602 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-08-01 14:26:58,605 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-08-01 14:26:58,605 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-08-01 14:26:58,605 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-08-01 14:26:58,605 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-08-01 14:26:58,605 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-08-01 14:26:58,607 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-08-01 14:26:58,607 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-08-01 14:26:58,607 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-08-01 14:26:58,607 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-08-01 14:26:58,607 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-08-01 14:26:58,610 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-08-01 14:26:58,610 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-08-01 14:26:58,610 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-08-01 14:26:58,610 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-08-01 14:26:58,610 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-08-01 14:26:58,614 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-08-01 14:26:58,614 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-08-01 14:26:58,614 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-08-01 14:26:58,614 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-08-01 14:26:58,614 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-08-01 14:26:58,616 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-08-01 14:26:58,616 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-08-01 14:26:58,616 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-08-01 14:26:58,616 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-08-01 14:26:58,616 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-08-01 14:26:58,618 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-08-01 14:26:58,618 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-08-01 14:26:58,618 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-08-01 14:26:58,618 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-08-01 14:26:58,618 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-08-01 14:26:58,623 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-08-01 14:26:58,623 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-08-01 14:26:58,623 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-08-01 14:26:58,623 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-08-01 14:26:58,623 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-08-01 14:26:58,629 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-08-01 14:26:58,629 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-08-01 14:26:58,629 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-08-01 14:26:58,629 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-08-01 14:26:58,629 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-08-01 14:26:58,632 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-08-01 14:26:58,632 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-08-01 14:26:58,632 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-08-01 14:26:58,632 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-08-01 14:26:58,632 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-08-01 14:26:58,635 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:26:58,635 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:26:58,635 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:26:58,635 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:26:58,635 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:26:58,638 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-08-01 14:26:58,638 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-08-01 14:26:58,638 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-08-01 14:26:58,638 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-08-01 14:26:58,638 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-08-01 14:26:58,639 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-08-01 14:26:58,639 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-08-01 14:26:58,639 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-08-01 14:26:58,639 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-08-01 14:26:58,639 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-08-01 14:26:58,641 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:26:58,641 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:26:58,641 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:26:58,641 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:26:58,641 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:26:58,644 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:26:58,644 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:26:58,644 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:26:58,644 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:26:58,644 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:26:58,653 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142658.png
2025-08-01 14:26:58,653 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142658.png
2025-08-01 14:26:58,653 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142658.png
2025-08-01 14:26:58,653 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142658.png
2025-08-01 14:26:58,653 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142658.png
2025-08-01 14:26:58,656 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:26:58,656 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:26:58,656 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:26:58,656 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:26:58,656 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:26:58,658 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-08-01 14:26:58,658 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-08-01 14:26:58,658 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-08-01 14:26:58,658 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-08-01 14:26:58,658 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-08-01 14:26:58,663 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142658.png
2025-08-01 14:26:58,663 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142658.png
2025-08-01 14:26:58,663 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142658.png
2025-08-01 14:26:58,663 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142658.png
2025-08-01 14:26:58,663 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142658.png
2025-08-01 14:26:58,687 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:26:58,687 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:26:58,687 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:26:58,687 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:26:58,687 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:26:58,690 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-08-01 14:26:58,690 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-08-01 14:26:58,690 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-08-01 14:26:58,690 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-08-01 14:26:58,690 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-08-01 14:26:58,692 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:26:58,692 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:26:58,692 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:26:58,692 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:26:58,692 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:26:58,698 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:26:58,698 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:26:58,698 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:26:58,698 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:26:58,698 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:26:59,000 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-08-01 14:26:59,000 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-08-01 14:26:59,000 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-08-01 14:26:59,000 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-08-01 14:26:59,000 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-08-01 14:26:59,770 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:26:59,770 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:26:59,770 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:26:59,770 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:26:59,770 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:26:59,771 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:26:59,771 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:26:59,771 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:26:59,771 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:26:59,771 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:26:59,773 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:26:59,773 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:26:59,773 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:26:59,773 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:26:59,773 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:26:59,774 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:26:59,774 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:26:59,774 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:26:59,774 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:26:59,774 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:26:59,778 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:26:59,778 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:26:59,778 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:26:59,778 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:26:59,778 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:26:59,780 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:26:59,780 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:26:59,780 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:26:59,780 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:26:59,780 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:26:59,783 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:26:59,783 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:26:59,783 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:26:59,783 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:26:59,783 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:26:59,784 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:26:59,784 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:26:59,784 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:26:59,784 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:26:59,784 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:26:59,785 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:26:59,785 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:26:59,785 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:26:59,785 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:26:59,785 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:26:59,788 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:26:59,788 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:26:59,788 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:26:59,788 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:26:59,788 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:26:59,789 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:26:59,789 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:26:59,789 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:26:59,789 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:26:59,789 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:27:00,407 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:00,407 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:00,407 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:00,407 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:00,407 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:00,743 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:00,743 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:00,743 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:00,743 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:00,743 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:00,988 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:00,988 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:00,988 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:00,988 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:00,988 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:01,235 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:01,235 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:01,235 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:01,235 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:01,235 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:01,493 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:01,493 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:01,493 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:01,493 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:01,493 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:01,741 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:01,741 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:01,741 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:01,741 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:01,741 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:01,986 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:01,986 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:01,986 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:01,986 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:01,986 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:02,232 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:02,232 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:02,232 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:02,232 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:02,232 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:02,480 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:02,480 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:02,480 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:02,480 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:02,480 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:02,809 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:02,809 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:02,809 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:02,809 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:02,809 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:03,032 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:27:03,032 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:27:03,032 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:27:03,032 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:27:03,032 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:27:03,033 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:27:03,033 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:27:03,033 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:27:03,033 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:27:03,033 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:27:03,035 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:27:03,035 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:27:03,035 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:27:03,035 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:27:03,035 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:27:40,469 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:27:40,469 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:27:40,469 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:27:40,469 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:27:40,469 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:27:40,469 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:27:40,472 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:27:40,472 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:27:40,472 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:27:40,472 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:27:40,472 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:27:40,472 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:27:40,474 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:27:40,474 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:27:40,474 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:27:40,474 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:27:40,474 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:27:40,474 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:27:40,480 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:27:40,480 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:27:40,480 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:27:40,480 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:27:40,480 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:27:40,480 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:27:40,484 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:27:40,484 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:27:40,484 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:27:40,484 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:27:40,484 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:27:40,484 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:27:40,488 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:27:40,488 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:27:40,488 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:27:40,488 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:27:40,488 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:27:40,488 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:27:40,493 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:27:40,493 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:27:40,493 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:27:40,493 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:27:40,493 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:27:40,493 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:27:40,497 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:27:40,497 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:27:40,497 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:27:40,497 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:27:40,497 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:27:40,497 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:27:40,498 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:27:40,498 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:27:40,498 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:27:40,498 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:27:40,498 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:27:40,498 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:27:41,000 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:27:41,000 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:27:41,000 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:27:41,000 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:27:41,000 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:27:41,000 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:27:41,001 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:27:41,001 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:27:41,001 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:27:41,001 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:27:41,001 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:27:41,001 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:27:41,056 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-08-01 14:27:41,056 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-08-01 14:27:41,056 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-08-01 14:27:41,056 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-08-01 14:27:41,056 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-08-01 14:27:41,056 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-08-01 14:27:41,058 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-08-01 14:27:41,058 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-08-01 14:27:41,058 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-08-01 14:27:41,058 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-08-01 14:27:41,058 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-08-01 14:27:41,058 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-08-01 14:27:41,063 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142741.png
2025-08-01 14:27:41,063 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142741.png
2025-08-01 14:27:41,063 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142741.png
2025-08-01 14:27:41,063 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142741.png
2025-08-01 14:27:41,063 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142741.png
2025-08-01 14:27:41,063 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142741.png
2025-08-01 14:27:41,066 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:27:41,066 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:27:41,066 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:27:41,066 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:27:41,066 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:27:41,066 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:27:41,071 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:27:41,071 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:27:41,071 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:27:41,071 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:27:41,071 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:27:41,071 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:27:41,079 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:27:41,079 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:27:41,079 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:27:41,079 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:27:41,079 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:27:41,079 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:27:41,082 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:27:41,082 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:27:41,082 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:27:41,082 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:27:41,082 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:27:41,082 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:27:41,085 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:27:41,085 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:27:41,085 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:27:41,085 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:27:41,085 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:27:41,085 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:27:41,095 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142741.png
2025-08-01 14:27:41,095 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142741.png
2025-08-01 14:27:41,095 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142741.png
2025-08-01 14:27:41,095 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142741.png
2025-08-01 14:27:41,095 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142741.png
2025-08-01 14:27:41,095 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142741.png
2025-08-01 14:27:41,098 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:27:41,098 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:27:41,098 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:27:41,098 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:27:41,098 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:27:41,098 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:27:41,100 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:27:41,100 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:27:41,100 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:27:41,100 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:27:41,100 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:27:41,100 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:27:41,104 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:27:41,104 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:27:41,104 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:27:41,104 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:27:41,104 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:27:41,104 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:27:41,112 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:27:41,112 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:27:41,112 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:27:41,112 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:27:41,112 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:27:41,112 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:27:41,114 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:27:41,114 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:27:41,114 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:27:41,114 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:27:41,114 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:27:41,114 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:27:41,116 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:27:41,116 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:27:41,116 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:27:41,116 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:27:41,116 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:27:41,116 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:27:41,119 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:27:41,119 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:27:41,119 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:27:41,119 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:27:41,119 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:27:41,119 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:27:41,122 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:27:41,122 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:27:41,122 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:27:41,122 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:27:41,122 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:27:41,122 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:27:41,128 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:27:41,128 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:27:41,128 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:27:41,128 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:27:41,128 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:27:41,128 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:27:41,130 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:27:41,130 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:27:41,130 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:27:41,130 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:27:41,130 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:27:41,130 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:27:41,143 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142741.png
2025-08-01 14:27:41,143 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142741.png
2025-08-01 14:27:41,143 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142741.png
2025-08-01 14:27:41,143 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142741.png
2025-08-01 14:27:41,143 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142741.png
2025-08-01 14:27:41,143 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142741.png
2025-08-01 14:27:41,149 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:27:41,149 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:27:41,149 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:27:41,149 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:27:41,149 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:27:41,149 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:27:41,152 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:27:41,152 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:27:41,152 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:27:41,152 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:27:41,152 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:27:41,152 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:27:41,165 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142741.png
2025-08-01 14:27:41,165 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142741.png
2025-08-01 14:27:41,165 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142741.png
2025-08-01 14:27:41,165 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142741.png
2025-08-01 14:27:41,165 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142741.png
2025-08-01 14:27:41,165 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142741.png
2025-08-01 14:27:41,196 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:27:41,196 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:27:41,196 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:27:41,196 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:27:41,196 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:27:41,196 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:27:41,200 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:27:41,200 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:27:41,200 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:27:41,200 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:27:41,200 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:27:41,200 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:27:41,202 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:27:41,202 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:27:41,202 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:27:41,202 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:27:41,202 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:27:41,202 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:27:41,205 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:27:41,205 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:27:41,205 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:27:41,205 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:27:41,205 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:27:41,205 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:27:41,513 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:27:41,513 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:27:41,513 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:27:41,513 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:27:41,513 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:27:41,513 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:27:42,309 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:27:42,309 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:27:42,309 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:27:42,309 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:27:42,309 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:27:42,309 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:27:42,311 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:27:42,311 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:27:42,311 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:27:42,311 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:27:42,311 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:27:42,311 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:27:42,312 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:27:42,312 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:27:42,312 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:27:42,312 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:27:42,312 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:27:42,312 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:27:42,314 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:27:42,314 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:27:42,314 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:27:42,314 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:27:42,314 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:27:42,314 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:27:42,319 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:27:42,319 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:27:42,319 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:27:42,319 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:27:42,319 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:27:42,319 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:27:42,323 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:27:42,323 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:27:42,323 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:27:42,323 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:27:42,323 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:27:42,323 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:27:42,330 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:27:42,330 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:27:42,330 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:27:42,330 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:27:42,330 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:27:42,330 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:27:42,331 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:27:42,331 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:27:42,331 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:27:42,331 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:27:42,331 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:27:42,331 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:27:42,334 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:27:42,334 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:27:42,334 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:27:42,334 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:27:42,334 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:27:42,334 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:27:42,335 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:27:42,335 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:27:42,335 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:27:42,335 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:27:42,335 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:27:42,335 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:27:42,336 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:27:42,336 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:27:42,336 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:27:42,336 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:27:42,336 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:27:42,336 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:27:42,866 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:42,866 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:42,866 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:42,866 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:42,866 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:42,866 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:43,148 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:43,148 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:43,148 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:43,148 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:43,148 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:43,148 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:43,394 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:43,394 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:43,394 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:43,394 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:43,394 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:43,394 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:43,693 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:43,693 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:43,693 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:43,693 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:43,693 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:43,693 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:43,942 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:43,942 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:43,942 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:43,942 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:43,942 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:43,942 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:44,226 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:44,226 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:44,226 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:44,226 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:44,226 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:44,226 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:44,476 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:44,476 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:44,476 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:44,476 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:44,476 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:44,476 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:44,723 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:44,723 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:44,723 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:44,723 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:44,723 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:44,723 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:44,972 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:44,972 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:44,972 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:44,972 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:44,972 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:44,972 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:45,291 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:45,291 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:45,291 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:45,291 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:45,291 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:45,291 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:27:45,512 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:27:45,512 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:27:45,512 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:27:45,512 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:27:45,512 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:27:45,512 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:27:45,514 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:27:45,514 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:27:45,514 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:27:45,514 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:27:45,514 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:27:45,514 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:27:45,516 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:27:45,516 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:27:45,516 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:27:45,516 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:27:45,516 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:27:45,516 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:28:22,670 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:28:22,670 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:28:22,670 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:28:22,670 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:28:22,670 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:28:22,670 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:28:22,670 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:28:22,673 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:28:22,673 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:28:22,673 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:28:22,673 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:28:22,673 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:28:22,673 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:28:22,673 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:28:22,675 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:28:22,675 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:28:22,675 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:28:22,675 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:28:22,675 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:28:22,675 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:28:22,675 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:28:22,679 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:28:22,679 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:28:22,679 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:28:22,679 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:28:22,679 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:28:22,679 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:28:22,679 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:28:22,684 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:28:22,684 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:28:22,684 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:28:22,684 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:28:22,684 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:28:22,684 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:28:22,684 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:28:22,693 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:28:22,693 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:28:22,693 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:28:22,693 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:28:22,693 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:28:22,693 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:28:22,693 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:28:22,696 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:28:22,696 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:28:22,696 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:28:22,696 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:28:22,696 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:28:22,696 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:28:22,696 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:28:22,697 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:28:22,697 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:28:22,697 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:28:22,697 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:28:22,697 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:28:22,697 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:28:22,697 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:28:22,699 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:28:22,699 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:28:22,699 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:28:22,699 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:28:22,699 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:28:22,699 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:28:22,699 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:28:23,201 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:28:23,201 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:28:23,201 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:28:23,201 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:28:23,201 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:28:23,201 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:28:23,201 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:28:23,204 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:28:23,204 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:28:23,204 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:28:23,204 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:28:23,204 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:28:23,204 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:28:23,204 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:28:23,269 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差36.23, 边缘比例0.0385
2025-08-01 14:28:23,269 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差36.23, 边缘比例0.0385
2025-08-01 14:28:23,269 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差36.23, 边缘比例0.0385
2025-08-01 14:28:23,269 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差36.23, 边缘比例0.0385
2025-08-01 14:28:23,269 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差36.23, 边缘比例0.0385
2025-08-01 14:28:23,269 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差36.23, 边缘比例0.0385
2025-08-01 14:28:23,269 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差36.23, 边缘比例0.0385
2025-08-01 14:28:23,278 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142823.png
2025-08-01 14:28:23,278 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142823.png
2025-08-01 14:28:23,278 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142823.png
2025-08-01 14:28:23,278 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142823.png
2025-08-01 14:28:23,278 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142823.png
2025-08-01 14:28:23,278 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142823.png
2025-08-01 14:28:23,278 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_142823.png
2025-08-01 14:28:23,282 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:28:23,282 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:28:23,282 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:28:23,282 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:28:23,282 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:28:23,282 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:28:23,282 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:28:23,283 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:28:23,283 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:28:23,283 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:28:23,283 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:28:23,283 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:28:23,283 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:28:23,283 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:28:23,284 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:28:23,284 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:28:23,284 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:28:23,284 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:28:23,284 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:28:23,284 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:28:23,284 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:28:23,288 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:28:23,288 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:28:23,288 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:28:23,288 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:28:23,288 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:28:23,288 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:28:23,288 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:28:23,293 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:28:23,293 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:28:23,293 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:28:23,293 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:28:23,293 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:28:23,293 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:28:23,293 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:28:23,297 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142823.png
2025-08-01 14:28:23,297 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142823.png
2025-08-01 14:28:23,297 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142823.png
2025-08-01 14:28:23,297 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142823.png
2025-08-01 14:28:23,297 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142823.png
2025-08-01 14:28:23,297 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142823.png
2025-08-01 14:28:23,297 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_142823.png
2025-08-01 14:28:23,300 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:28:23,300 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:28:23,300 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:28:23,300 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:28:23,300 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:28:23,300 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:28:23,300 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:28:23,309 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:28:23,309 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:28:23,309 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:28:23,309 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:28:23,309 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:28:23,309 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:28:23,309 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:28:23,313 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:28:23,313 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:28:23,313 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:28:23,313 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:28:23,313 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:28:23,313 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:28:23,313 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:28:23,317 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:28:23,317 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:28:23,317 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:28:23,317 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:28:23,317 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:28:23,317 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:28:23,317 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:28:23,319 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:28:23,319 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:28:23,319 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:28:23,319 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:28:23,319 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:28:23,319 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:28:23,319 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:28:23,326 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:28:23,326 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:28:23,326 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:28:23,326 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:28:23,326 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:28:23,326 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:28:23,326 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:28:23,332 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:28:23,332 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:28:23,332 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:28:23,332 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:28:23,332 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:28:23,332 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:28:23,332 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:28:23,339 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:28:23,339 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:28:23,339 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:28:23,339 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:28:23,339 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:28:23,339 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:28:23,339 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:28:23,346 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:28:23,346 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:28:23,346 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:28:23,346 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:28:23,346 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:28:23,346 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:28:23,346 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:28:23,352 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:28:23,352 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:28:23,352 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:28:23,352 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:28:23,352 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:28:23,352 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:28:23,352 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:28:23,367 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142823.png
2025-08-01 14:28:23,367 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142823.png
2025-08-01 14:28:23,367 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142823.png
2025-08-01 14:28:23,367 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142823.png
2025-08-01 14:28:23,367 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142823.png
2025-08-01 14:28:23,367 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142823.png
2025-08-01 14:28:23,367 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_142823.png
2025-08-01 14:28:23,386 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:28:23,386 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:28:23,386 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:28:23,386 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:28:23,386 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:28:23,386 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:28:23,386 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:28:23,398 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:28:23,398 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:28:23,398 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:28:23,398 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:28:23,398 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:28:23,398 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:28:23,398 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:28:23,403 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142823.png
2025-08-01 14:28:23,403 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142823.png
2025-08-01 14:28:23,403 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142823.png
2025-08-01 14:28:23,403 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142823.png
2025-08-01 14:28:23,403 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142823.png
2025-08-01 14:28:23,403 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142823.png
2025-08-01 14:28:23,403 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_142823.png
2025-08-01 14:28:23,434 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:28:23,434 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:28:23,434 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:28:23,434 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:28:23,434 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:28:23,434 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:28:23,434 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:28:23,436 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:28:23,436 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:28:23,436 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:28:23,436 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:28:23,436 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:28:23,436 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:28:23,436 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:28:23,443 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:28:23,443 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:28:23,443 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:28:23,443 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:28:23,443 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:28:23,443 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:28:23,443 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:28:23,445 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:28:23,445 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:28:23,445 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:28:23,445 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:28:23,445 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:28:23,445 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:28:23,445 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:28:23,749 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:28:23,749 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:28:23,749 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:28:23,749 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:28:23,749 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:28:23,749 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:28:23,749 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:28:24,524 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:28:24,524 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:28:24,524 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:28:24,524 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:28:24,524 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:28:24,524 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:28:24,524 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:28:24,526 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:28:24,526 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:28:24,526 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:28:24,526 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:28:24,526 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:28:24,526 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:28:24,526 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:28:24,528 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:28:24,528 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:28:24,528 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:28:24,528 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:28:24,528 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:28:24,528 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:28:24,528 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:28:24,530 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:28:24,530 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:28:24,530 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:28:24,530 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:28:24,530 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:28:24,530 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:28:24,530 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:28:24,536 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:28:24,536 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:28:24,536 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:28:24,536 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:28:24,536 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:28:24,536 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:28:24,536 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:28:24,542 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:28:24,542 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:28:24,542 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:28:24,542 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:28:24,542 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:28:24,542 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:28:24,542 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:28:24,546 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:28:24,546 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:28:24,546 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:28:24,546 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:28:24,546 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:28:24,546 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:28:24,546 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:28:24,549 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:28:24,549 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:28:24,549 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:28:24,549 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:28:24,549 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:28:24,549 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:28:24,549 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:28:24,550 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:28:24,550 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:28:24,550 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:28:24,550 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:28:24,550 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:28:24,550 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:28:24,550 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:28:24,560 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:28:24,560 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:28:24,560 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:28:24,560 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:28:24,560 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:28:24,560 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:28:24,560 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:28:24,565 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:28:24,565 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:28:24,565 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:28:24,565 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:28:24,565 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:28:24,565 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:28:24,565 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:28:25,096 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,096 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,096 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,096 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,096 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,096 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,096 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,348 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,348 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,348 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,348 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,348 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,348 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,348 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,591 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,591 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,591 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,591 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,591 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,591 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,591 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,840 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,840 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,840 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,840 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,840 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,840 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:25,840 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,094 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,094 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,094 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,094 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,094 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,094 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,094 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,371 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,371 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,371 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,371 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,371 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,371 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,371 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,618 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,618 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,618 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,618 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,618 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,618 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,618 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,864 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,864 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,864 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,864 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,864 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,864 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:26,864 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:27,254 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:27,254 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:27,254 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:27,254 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:27,254 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:27,254 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:27,254 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:27,499 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:27,499 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:27,499 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:27,499 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:27,499 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:27,499 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:27,499 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:28:27,720 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:28:27,720 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:28:27,720 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:28:27,720 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:28:27,720 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:28:27,720 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:28:27,720 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:28:27,722 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:28:27,722 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:28:27,722 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:28:27,722 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:28:27,722 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:28:27,722 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:28:27,722 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:28:27,723 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:28:27,723 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:28:27,723 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:28:27,723 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:28:27,723 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:28:27,723 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:28:27,723 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:30:13,664 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:30:46,377 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:30:46,377 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:30:46,382 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:30:46,382 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:30:46,400 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:30:46,400 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:30:46,476 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:30:46,476 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:30:46,508 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:30:46,508 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:30:46,624 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:30:46,624 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:30:46,624 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:30:46,624 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:30:46,625 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:30:46,625 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:30:46,626 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:30:46,626 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:30:47,127 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:30:47,127 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:30:47,128 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:30:47,128 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:30:47,209 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-08-01 14:30:47,209 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-08-01 14:30:47,210 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-08-01 14:30:47,210 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-08-01 14:30:47,216 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_143047.png
2025-08-01 14:30:47,216 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_143047.png
2025-08-01 14:30:47,218 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:30:47,218 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:30:47,219 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:30:47,219 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:30:47,220 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:30:47,220 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:30:47,222 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:30:47,222 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:30:47,225 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:30:47,225 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:30:47,229 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_143047.png
2025-08-01 14:30:47,229 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_143047.png
2025-08-01 14:30:47,241 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:30:47,241 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:30:47,244 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:30:47,244 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:30:47,246 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:30:47,246 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:30:47,250 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:30:47,250 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:30:47,252 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:30:47,252 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:30:47,253 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:30:47,253 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:30:47,254 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:30:47,254 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:30:47,259 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:30:47,259 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:30:47,263 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:30:47,263 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:30:47,265 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:30:47,265 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:30:47,276 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_143047.png
2025-08-01 14:30:47,276 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_143047.png
2025-08-01 14:30:47,281 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:30:47,281 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:30:47,284 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:30:47,284 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:30:47,287 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_143047.png
2025-08-01 14:30:47,287 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_143047.png
2025-08-01 14:30:47,365 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:30:47,365 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:30:47,366 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:30:47,366 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:30:47,367 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:30:47,367 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:30:47,368 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:30:47,368 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:30:47,669 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:30:47,669 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:30:48,449 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:30:48,449 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:30:48,450 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:30:48,450 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:30:48,451 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:30:48,451 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:30:48,451 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:30:48,451 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:30:48,452 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:30:48,452 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:30:48,455 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:30:48,455 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:30:48,457 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:30:48,457 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:30:48,458 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:30:48,458 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:30:48,458 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:30:48,458 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:30:48,459 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:30:48,459 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:30:48,459 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:30:48,459 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:30:48,985 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:30:48,985 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:30:49,306 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:30:49,306 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:30:49,553 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:30:49,553 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:30:49,802 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:30:49,802 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:30:50,050 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:30:50,050 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:30:50,371 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:30:50,371 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:30:50,616 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:30:50,616 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:30:50,867 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:30:50,867 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:30:51,116 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:30:51,116 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:30:51,450 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:30:51,450 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:30:51,672 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:30:51,672 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:30:51,673 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:30:51,673 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:30:51,673 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:30:51,673 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:31:28,907 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:31:28,907 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:31:28,907 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:31:28,910 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:31:28,910 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:31:28,910 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:31:28,911 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:31:28,911 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:31:28,911 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:31:28,914 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:31:28,914 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:31:28,914 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:31:28,915 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:31:28,915 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:31:28,915 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:31:28,919 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:31:28,919 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:31:28,919 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:31:28,923 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:31:28,923 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:31:28,923 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:31:28,926 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:31:28,926 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:31:28,926 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:31:28,928 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:31:28,928 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:31:28,928 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:31:29,429 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:31:29,429 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:31:29,429 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:31:29,430 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:31:29,430 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:31:29,430 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:31:29,493 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差36.32, 边缘比例0.0382
2025-08-01 14:31:29,493 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差36.32, 边缘比例0.0382
2025-08-01 14:31:29,493 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差36.32, 边缘比例0.0382
2025-08-01 14:31:29,497 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_143129.png
2025-08-01 14:31:29,497 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_143129.png
2025-08-01 14:31:29,497 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_143129.png
2025-08-01 14:31:29,499 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:31:29,499 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:31:29,499 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:31:29,500 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:31:29,500 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:31:29,500 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:31:29,505 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:31:29,505 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:31:29,505 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:31:29,508 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:31:29,508 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:31:29,508 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:31:29,510 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:31:29,510 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:31:29,510 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:31:29,517 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_143129.png
2025-08-01 14:31:29,517 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_143129.png
2025-08-01 14:31:29,517 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_143129.png
2025-08-01 14:31:29,520 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:31:29,520 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:31:29,520 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-01 14:31:29,521 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:31:29,521 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:31:29,521 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:31:29,522 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:31:29,522 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:31:29,522 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:31:29,523 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:31:29,523 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:31:29,523 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-01 14:31:29,525 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:31:29,525 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:31:29,525 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-01 14:31:29,530 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:31:29,530 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:31:29,530 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:31:29,533 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:31:29,533 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:31:29,533 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-01 14:31:29,536 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:31:29,536 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:31:29,536 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-01 14:31:29,537 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:31:29,537 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:31:29,537 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:31:29,538 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:31:29,538 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:31:29,538 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:31:29,552 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_143129.png
2025-08-01 14:31:29,552 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_143129.png
2025-08-01 14:31:29,552 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_143129.png
2025-08-01 14:31:29,554 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:31:29,554 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:31:29,554 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:31:29,555 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:31:29,555 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:31:29,555 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-01 14:31:29,557 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_143129.png
2025-08-01 14:31:29,557 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_143129.png
2025-08-01 14:31:29,557 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_143129.png
2025-08-01 14:31:29,585 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:31:29,585 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:31:29,585 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:31:29,589 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:31:29,589 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:31:29,589 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-01 14:31:29,594 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:31:29,594 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:31:29,594 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:31:29,602 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:31:29,602 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:31:29,602 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:31:29,903 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:31:29,903 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:31:29,903 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-01 14:31:30,681 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:31:30,681 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:31:30,681 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:31:30,681 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:31:30,681 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:31:30,681 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:31:30,682 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:31:30,682 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:31:30,682 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:31:30,683 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:31:30,683 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:31:30,683 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:31:30,684 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:31:30,684 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:31:30,684 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:31:30,685 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:31:30,685 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:31:30,685 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:31:30,690 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:31:30,690 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:31:30,690 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:31:30,691 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:31:30,691 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:31:30,691 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:31:30,695 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:31:30,695 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:31:30,695 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:31:30,697 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:31:30,697 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:31:30,697 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:31:30,698 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:31:30,698 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:31:30,698 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:31:31,227 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:31,227 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:31,227 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:31,474 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:31,474 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:31,474 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:31,719 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:31,719 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:31,719 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:31,966 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:31,966 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:31,966 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:32,211 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:32,211 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:32,211 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:32,480 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:32,480 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:32,480 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:32,739 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:32,739 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:32,739 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:32,984 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:32,984 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:32,984 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:33,233 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:33,233 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:33,233 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:33,541 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:33,541 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:33,541 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:31:33,765 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:31:33,765 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:31:33,765 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:31:33,766 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:31:33,766 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:31:33,766 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:31:33,767 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:31:33,767 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:31:33,767 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:48:49,218 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:49:24,730 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:49:24,730 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:49:24,734 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:49:24,734 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-01 14:49:24,743 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:49:24,743 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:49:24,761 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:49:24,761 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:49:24,779 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:49:24,779 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:49:24,799 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:49:24,799 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:49:24,805 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:49:24,805 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:49:24,806 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:49:24,806 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:49:24,812 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:49:24,812 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:49:25,319 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:49:25,319 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:49:25,321 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:49:25,321 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:49:25,399 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差40.16, 边缘比例0.0475
2025-08-01 14:49:25,399 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差40.16, 边缘比例0.0475
2025-08-01 14:49:25,406 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_144925.png
2025-08-01 14:49:25,406 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_144925.png
2025-08-01 14:49:25,408 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:49:25,408 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:49:25,413 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:49:25,413 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:49:25,416 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:49:25,416 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:49:25,418 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:49:25,418 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:49:25,425 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:49:25,425 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:49:25,444 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_144925.png
2025-08-01 14:49:25,444 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_144925.png
2025-08-01 14:49:25,451 - WeChatAutoAdd - INFO - 底部区域原始检测到 27 个轮廓
2025-08-01 14:49:25,451 - WeChatAutoAdd - INFO - 底部区域原始检测到 27 个轮廓
2025-08-01 14:49:25,453 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:49:25,453 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:49:25,467 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:49:25,467 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:49:25,469 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,323), 尺寸128x30, 长宽比4.27, 已知特征:True
2025-08-01 14:49:25,469 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,323), 尺寸128x30, 长宽比4.27, 已知特征:True
2025-08-01 14:49:25,476 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=323 (距底部154像素区域)
2025-08-01 14:49:25,476 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=323 (距底部154像素区域)
2025-08-01 14:49:25,480 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-08-01 14:49:25,480 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-08-01 14:49:25,482 - WeChatAutoAdd - INFO - 发现已知按钮特征候选: 位置(100,323), 尺寸128x30
2025-08-01 14:49:25,482 - WeChatAutoAdd - INFO - 发现已知按钮特征候选: 位置(100,323), 尺寸128x30
2025-08-01 14:49:25,484 - WeChatAutoAdd - INFO - 发现已知按钮特征: 位置(100,323), 尺寸128x30
2025-08-01 14:49:25,484 - WeChatAutoAdd - INFO - 发现已知按钮特征: 位置(100,323), 尺寸128x30
2025-08-01 14:49:25,485 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,259), 尺寸8x4, 长宽比2.00, 面积32
2025-08-01 14:49:25,485 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,259), 尺寸8x4, 长宽比2.00, 面积32
2025-08-01 14:49:25,486 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,258), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:49:25,486 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,258), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:49:25,496 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(49,257), 尺寸1x5, 长宽比0.20, 面积5
2025-08-01 14:49:25,496 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(49,257), 尺寸1x5, 长宽比0.20, 面积5
2025-08-01 14:49:25,498 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(47,257), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:49:25,498 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(47,257), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:49:25,504 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(71,256), 尺寸2x3, 长宽比0.67, 面积6
2025-08-01 14:49:25,504 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(71,256), 尺寸2x3, 长宽比0.67, 面积6
2025-08-01 14:49:25,514 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,255), 尺寸2x3, 长宽比0.67, 面积6
2025-08-01 14:49:25,514 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,255), 尺寸2x3, 长宽比0.67, 面积6
2025-08-01 14:49:25,518 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,254), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:49:25,518 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,254), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:49:25,539 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,253), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:49:25,539 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,253), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:49:25,542 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,253), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:49:25,542 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,253), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:49:25,548 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(55,251), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:49:25,548 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(55,251), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:49:25,553 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-08-01 14:49:25,553 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-08-01 14:49:25,557 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=69.5 (阈值:60)
2025-08-01 14:49:25,557 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=69.5 (阈值:60)
2025-08-01 14:49:25,564 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(52,249), 尺寸39x14, 长宽比2.79, 面积546
2025-08-01 14:49:25,564 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(52,249), 尺寸39x14, 长宽比2.79, 面积546
2025-08-01 14:49:25,569 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(51,249), 尺寸25x12, 长宽比2.08, 面积300
2025-08-01 14:49:25,569 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(51,249), 尺寸25x12, 长宽比2.08, 面积300
2025-08-01 14:49:25,574 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(48,249), 尺寸13x7, 长宽比1.86, 面积91
2025-08-01 14:49:25,574 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(48,249), 尺寸13x7, 长宽比1.86, 面积91
2025-08-01 14:49:25,577 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,240), 尺寸1x2, 长宽比0.50, 面积2
2025-08-01 14:49:25,577 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,240), 尺寸1x2, 长宽比0.50, 面积2
2025-08-01 14:49:25,583 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(136,240), 尺寸6x3, 长宽比2.00, 面积18
2025-08-01 14:49:25,583 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(136,240), 尺寸6x3, 长宽比2.00, 面积18
2025-08-01 14:49:25,587 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(144,238), 尺寸4x3, 长宽比1.33, 面积12
2025-08-01 14:49:25,587 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(144,238), 尺寸4x3, 长宽比1.33, 面积12
2025-08-01 14:49:25,590 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,235), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:49:25,590 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,235), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:49:25,591 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(137,235), 尺寸5x6, 长宽比0.83, 面积30
2025-08-01 14:49:25,591 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(137,235), 尺寸5x6, 长宽比0.83, 面积30
2025-08-01 14:49:25,596 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(150,233), 尺寸17x12, 长宽比1.42, 面积204
2025-08-01 14:49:25,596 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(150,233), 尺寸17x12, 长宽比1.42, 面积204
2025-08-01 14:49:25,607 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=88.4 (阈值:60)
2025-08-01 14:49:25,607 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=88.4 (阈值:60)
2025-08-01 14:49:25,623 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(142,233), 尺寸8x10, 长宽比0.80, 面积80
2025-08-01 14:49:25,623 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(142,233), 尺寸8x10, 长宽比0.80, 面积80
2025-08-01 14:49:25,634 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,230), 尺寸8x12, 长宽比0.67, 面积96
2025-08-01 14:49:25,634 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,230), 尺寸8x12, 长宽比0.67, 面积96
2025-08-01 14:49:25,635 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(122,230), 尺寸20x13, 长宽比1.54, 面积260
2025-08-01 14:49:25,635 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(122,230), 尺寸20x13, 长宽比1.54, 面积260
2025-08-01 14:49:25,638 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=83.4 (阈值:60)
2025-08-01 14:49:25,638 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=83.4 (阈值:60)
2025-08-01 14:49:25,639 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:49:25,639 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:49:25,640 - WeChatAutoAdd - INFO - 底部区域找到 3 个按钮候选
2025-08-01 14:49:25,640 - WeChatAutoAdd - INFO - 底部区域找到 3 个按钮候选
2025-08-01 14:49:25,641 - WeChatAutoAdd - INFO - 选择已知按钮特征: Y=323, 很可能是'添加到通讯录'按钮
2025-08-01 14:49:25,641 - WeChatAutoAdd - INFO - 选择已知按钮特征: Y=323, 很可能是'添加到通讯录'按钮
2025-08-01 14:49:25,644 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 338), 尺寸: 128x30, 位置得分: 5.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-08-01 14:49:25,644 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 338), 尺寸: 128x30, 位置得分: 5.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-08-01 14:49:25,648 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-08-01 14:49:25,648 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-08-01 14:49:25,654 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_144925.png
2025-08-01 14:49:25,654 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_144925.png
2025-08-01 14:49:25,657 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-08-01 14:49:25,657 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-08-01 14:49:25,667 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:49:25,667 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:49:25,972 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 338) -> 屏幕坐标(1364, 338)
2025-08-01 14:49:25,972 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 338) -> 屏幕坐标(1364, 338)
2025-08-01 14:49:26,747 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:49:26,747 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:49:26,748 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:49:26,748 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:49:26,749 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:49:26,749 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:49:26,750 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:49:26,750 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:49:26,751 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:49:26,751 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:49:26,752 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:49:26,752 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:49:26,756 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:49:26,756 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:49:26,757 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:49:26,757 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:49:26,757 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:49:26,757 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:49:26,758 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:49:26,758 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:49:26,762 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:49:26,762 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:49:28,129 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:49:28,129 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:49:28,687 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:49:28,687 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:49:28,955 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:49:28,955 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:49:29,387 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:49:29,387 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:49:29,643 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:49:29,643 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:49:29,873 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:49:29,873 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:49:29,879 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:49:29,879 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:49:29,885 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:49:29,885 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:51:38,809 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:52:10,579 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:52:10,579 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-01 14:52:10,581 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-08-01 14:52:10,581 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-08-01 14:52:10,582 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:52:10,582 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:52:10,585 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:52:10,585 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:52:10,586 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:52:10,586 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:52:10,589 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:52:10,589 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:52:10,591 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:52:10,591 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:52:10,591 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:52:10,591 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:52:10,593 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:52:10,593 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:52:11,097 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:52:11,097 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:52:11,098 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:52:11,098 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:52:11,173 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差26.19, 边缘比例0.0351
2025-08-01 14:52:11,173 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差26.19, 边缘比例0.0351
2025-08-01 14:52:11,178 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_145211.png
2025-08-01 14:52:11,178 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_145211.png
2025-08-01 14:52:11,181 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:52:11,181 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:52:11,182 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:52:11,182 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:52:11,184 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:52:11,184 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:52:11,192 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:52:11,192 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:52:11,211 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:52:11,211 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:52:11,229 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_145211.png
2025-08-01 14:52:11,229 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_145211.png
2025-08-01 14:52:11,231 - WeChatAutoAdd - INFO - 底部区域原始检测到 40 个轮廓
2025-08-01 14:52:11,231 - WeChatAutoAdd - INFO - 底部区域原始检测到 40 个轮廓
2025-08-01 14:52:11,245 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,453), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:52:11,245 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,453), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:52:11,245 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:52:11,245 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:52:11,247 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:52:11,247 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:52:11,248 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:52:11,248 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:52:11,252 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:52:11,252 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:52:11,254 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-08-01 14:52:11,254 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-08-01 14:52:11,255 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:52:11,255 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:52:11,256 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-08-01 14:52:11,256 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-08-01 14:52:11,257 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-08-01 14:52:11,257 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-08-01 14:52:11,262 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:52:11,262 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:52:11,265 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-08-01 14:52:11,265 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-08-01 14:52:11,272 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-08-01 14:52:11,272 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-08-01 14:52:11,277 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-08-01 14:52:11,277 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-08-01 14:52:11,281 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:52:11,281 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:52:11,287 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:52:11,287 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-08-01 14:52:11,289 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:52:11,289 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-08-01 14:52:11,291 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:52:11,291 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:52:11,294 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-08-01 14:52:11,294 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-08-01 14:52:11,295 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-08-01 14:52:11,295 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-08-01 14:52:11,296 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:52:11,296 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:52:11,297 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:52:11,297 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:52:11,299 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-08-01 14:52:11,299 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-08-01 14:52:11,305 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-08-01 14:52:11,305 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-08-01 14:52:11,308 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-08-01 14:52:11,308 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-08-01 14:52:11,310 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:52:11,310 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:52:11,313 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-08-01 14:52:11,313 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-08-01 14:52:11,316 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-08-01 14:52:11,316 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-08-01 14:52:11,321 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-08-01 14:52:11,321 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-08-01 14:52:11,324 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-08-01 14:52:11,324 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-08-01 14:52:11,327 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-08-01 14:52:11,327 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-08-01 14:52:11,328 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-08-01 14:52:11,328 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-08-01 14:52:11,329 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-08-01 14:52:11,329 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-08-01 14:52:11,330 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-08-01 14:52:11,330 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-08-01 14:52:11,334 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-08-01 14:52:11,334 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-08-01 14:52:11,337 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-08-01 14:52:11,337 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-08-01 14:52:11,338 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-08-01 14:52:11,338 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-08-01 14:52:11,339 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-08-01 14:52:11,339 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-08-01 14:52:11,340 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-08-01 14:52:11,340 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-08-01 14:52:11,341 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-08-01 14:52:11,341 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-08-01 14:52:11,343 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:52:11,343 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:52:11,344 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-08-01 14:52:11,344 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-08-01 14:52:11,345 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-08-01 14:52:11,345 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-08-01 14:52:11,346 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:52:11,346 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-01 14:52:11,347 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:52:11,347 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-01 14:52:11,354 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_145211.png
2025-08-01 14:52:11,354 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250801_145211.png
2025-08-01 14:52:11,356 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:52:11,356 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-01 14:52:11,360 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-08-01 14:52:11,360 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-08-01 14:52:11,367 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_145211.png
2025-08-01 14:52:11,367 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250801_145211.png
2025-08-01 14:52:11,433 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:52:11,433 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-01 14:52:11,436 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-08-01 14:52:11,436 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-08-01 14:52:11,437 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:52:11,437 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-01 14:52:11,437 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:52:11,437 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-01 14:52:11,738 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-08-01 14:52:11,738 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-08-01 14:52:12,520 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:52:12,520 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-01 14:52:12,521 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:52:12,521 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-01 14:52:12,522 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:52:12,522 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-01 14:52:12,522 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:52:12,522 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-01 14:52:12,523 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:52:12,523 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-01 14:52:12,524 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:52:12,524 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-01 14:52:12,526 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:52:12,526 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 1 个
2025-08-01 14:52:12,526 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:52:12,526 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-01 14:52:12,527 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:52:12,527 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-01 14:52:12,528 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:52:12,528 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-01 14:52:12,529 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:52:12,529 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-01 14:52:13,049 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:52:13,049 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:52:13,304 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:52:13,304 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:52:13,553 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:52:13,553 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:52:13,802 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:52:13,802 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:52:14,051 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:52:14,051 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:52:14,349 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:52:14,349 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:52:14,592 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:52:14,592 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:52:14,839 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:52:14,839 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:52:15,087 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:52:15,087 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:52:15,336 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:52:15,336 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-01 14:52:15,622 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:52:15,622 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-01 14:52:15,623 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:52:15,623 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-01 14:52:15,623 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:52:15,623 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-01 14:52:48,003 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:52:48,003 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:52:48,007 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:52:48,007 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:52:48,008 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:52:48,008 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:52:48,020 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:52:48,020 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:52:48,023 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:52:48,023 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:52:48,024 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:52:48,024 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:52:48,025 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:52:48,025 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:52:48,526 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:52:48,526 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:52:48,527 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:52:48,527 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:52:48,595 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差23.34, 边缘比例0.0215
2025-08-01 14:52:48,595 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差23.34, 边缘比例0.0215
2025-08-01 14:52:48,601 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_145248.png
2025-08-01 14:52:48,601 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_145248.png
2025-08-01 14:52:48,604 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:52:48,604 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:52:48,612 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:52:48,612 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:52:48,619 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:52:48,619 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:52:48,621 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:52:48,621 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:52:48,622 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:52:48,622 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:52:48,626 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_145248.png
2025-08-01 14:52:48,626 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_145248.png
2025-08-01 14:52:48,627 - WeChatAutoAdd - INFO - 底部区域原始检测到 3 个轮廓
2025-08-01 14:52:48,627 - WeChatAutoAdd - INFO - 底部区域原始检测到 3 个轮廓
2025-08-01 14:52:48,630 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,453), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:52:48,630 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,453), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:52:48,634 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:52:48,634 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:52:48,637 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:52:48,637 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:52:48,638 - WeChatAutoAdd - WARNING - 底部245像素区域未找到符合条件的按钮
2025-08-01 14:52:48,638 - WeChatAutoAdd - WARNING - 底部245像素区域未找到符合条件的按钮
2025-08-01 14:52:48,639 - WeChatAutoAdd - WARNING - 底部区域未找到按钮
2025-08-01 14:52:48,639 - WeChatAutoAdd - WARNING - 底部区域未找到按钮
2025-08-01 14:52:48,641 - WeChatAutoAdd - ERROR - 无法找到'添加到通讯录'按钮，结束当前流程
2025-08-01 14:52:48,641 - WeChatAutoAdd - ERROR - 无法找到'添加到通讯录'按钮，结束当前流程
2025-08-01 14:53:17,199 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:53:17,199 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:53:17,203 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:53:17,203 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:53:17,204 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:53:17,204 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:53:17,323 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:53:17,323 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:53:17,324 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:53:17,324 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:53:17,325 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:53:17,325 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:53:17,326 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:53:17,326 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:53:17,827 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:53:17,827 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:53:17,828 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:53:17,828 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:53:17,904 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差23.34, 边缘比例0.0215
2025-08-01 14:53:17,904 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差23.34, 边缘比例0.0215
2025-08-01 14:53:17,908 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_145317.png
2025-08-01 14:53:17,908 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_145317.png
2025-08-01 14:53:17,909 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:53:17,909 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:53:17,910 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:53:17,910 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:53:17,911 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:53:17,911 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:53:17,913 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:53:17,913 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:53:17,913 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:53:17,913 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:53:17,921 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_145317.png
2025-08-01 14:53:17,921 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_145317.png
2025-08-01 14:53:17,922 - WeChatAutoAdd - INFO - 底部区域原始检测到 3 个轮廓
2025-08-01 14:53:17,922 - WeChatAutoAdd - INFO - 底部区域原始检测到 3 个轮廓
2025-08-01 14:53:17,923 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,453), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:53:17,923 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,453), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:53:17,924 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:53:17,924 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:53:17,925 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:53:17,925 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:53:17,926 - WeChatAutoAdd - WARNING - 底部245像素区域未找到符合条件的按钮
2025-08-01 14:53:17,926 - WeChatAutoAdd - WARNING - 底部245像素区域未找到符合条件的按钮
2025-08-01 14:53:17,927 - WeChatAutoAdd - WARNING - 底部区域未找到按钮
2025-08-01 14:53:17,927 - WeChatAutoAdd - WARNING - 底部区域未找到按钮
2025-08-01 14:53:17,928 - WeChatAutoAdd - ERROR - 无法找到'添加到通讯录'按钮，结束当前流程
2025-08-01 14:53:17,928 - WeChatAutoAdd - ERROR - 无法找到'添加到通讯录'按钮，结束当前流程
2025-08-01 14:53:44,723 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:53:44,723 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-01 14:53:44,727 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:53:44,727 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-01 14:53:44,727 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:53:44,727 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-01 14:53:44,744 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:53:44,744 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-01 14:53:44,746 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:53:44,746 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-01 14:53:44,747 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:53:44,747 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-01 14:53:44,748 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:53:44,748 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-01 14:53:45,251 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:53:45,251 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-01 14:53:45,251 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:53:45,251 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-01 14:53:45,320 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差23.34, 边缘比例0.0215
2025-08-01 14:53:45,320 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差23.34, 边缘比例0.0215
2025-08-01 14:53:45,324 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_145345.png
2025-08-01 14:53:45,324 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250801_145345.png
2025-08-01 14:53:45,325 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:53:45,325 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-01 14:53:45,328 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:53:45,328 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-01 14:53:45,330 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:53:45,330 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-01 14:53:45,332 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:53:45,332 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-01 14:53:45,333 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:53:45,333 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-01 14:53:45,336 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_145345.png
2025-08-01 14:53:45,336 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250801_145345.png
2025-08-01 14:53:45,337 - WeChatAutoAdd - INFO - 底部区域原始检测到 3 个轮廓
2025-08-01 14:53:45,337 - WeChatAutoAdd - INFO - 底部区域原始检测到 3 个轮廓
2025-08-01 14:53:45,338 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,453), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:53:45,338 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,453), 尺寸1x1, 长宽比1.00, 面积1
2025-08-01 14:53:45,339 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:53:45,339 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-01 14:53:45,339 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:53:45,339 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-01 14:53:45,340 - WeChatAutoAdd - WARNING - 底部245像素区域未找到符合条件的按钮
2025-08-01 14:53:45,340 - WeChatAutoAdd - WARNING - 底部245像素区域未找到符合条件的按钮
2025-08-01 14:53:45,340 - WeChatAutoAdd - WARNING - 底部区域未找到按钮
2025-08-01 14:53:45,340 - WeChatAutoAdd - WARNING - 底部区域未找到按钮
2025-08-01 14:53:45,341 - WeChatAutoAdd - ERROR - 无法找到'添加到通讯录'按钮，结束当前流程
2025-08-01 14:53:45,341 - WeChatAutoAdd - ERROR - 无法找到'添加到通讯录'按钮，结束当前流程
