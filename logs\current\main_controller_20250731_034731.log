2025-07-31 03:47:31,908 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-31 03:47:31,911 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-31 03:47:31,921 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 有效微信窗口统计: 0 个
2025-07-31 03:47:31,934 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 0 个微信窗口
2025-07-31 03:47:31,944 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-31 03:47:31,950 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-31 03:47:31,955 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-31 03:47:31,959 - WeChatAutoAdd - INFO - 创建截图目录: screenshots
2025-07-31 03:47:31,963 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-31 03:47:31,966 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-31 03:47:31,967 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-31 03:47:31,995 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-31 03:47:32,008 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-31 03:47:32,015 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-31 03:47:32,015 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-31 03:47:32,029 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-31 03:47:32,038 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250731_034732.log
2025-07-31 03:47:32,054 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-31 03:47:32,057 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-31 03:47:32,061 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-31 03:47:32,062 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-31 03:47:32,066 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-31 03:47:32,067 - main_controller - INFO - 🧠 启用智能检测功能...
2025-07-31 03:47:32,070 - main_controller - INFO - ✅ 智能检测功能已启用
2025-07-31 03:47:32,073 - main_controller - INFO - 🔍 支持的检测方法:
2025-07-31 03:47:32,074 - main_controller - INFO -    1. 通过子控件检测确定按钮
2025-07-31 03:47:32,075 - main_controller - INFO -    2. 通过文本识别检测确定按钮
2025-07-31 03:47:32,078 - main_controller - INFO -    3. 通过图像识别检测确定按钮
2025-07-31 03:47:32,084 - main_controller - INFO -    4. 基于相对位置计算确定按钮坐标
2025-07-31 03:47:32,089 - main_controller - INFO - 🖱️ 支持的点击方法:
2025-07-31 03:47:32,091 - main_controller - INFO -    1. 使用Win32 API点击
2025-07-31 03:47:32,092 - main_controller - INFO -    2. 使用PyAutoGUI点击
2025-07-31 03:47:32,097 - main_controller - INFO -    3. 使用键盘Enter键
2025-07-31 03:47:32,105 - main_controller - INFO -    4. 发送窗口消息
2025-07-31 03:47:32,106 - main_controller - INFO - 🔄 重试设置: 检测最多3次, 点击最多4次
2025-07-31 03:47:32,107 - main_controller - INFO - 📱 已启用跨分辨率兼容性
2025-07-31 03:47:32,112 - main_controller - INFO - 🪟 已启用窗口位置无关性
2025-07-31 03:47:32,114 - main_controller - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-31 03:47:32,133 - main_controller - INFO - 📅 当前北京时间: 2025-07-31 11:47:32
2025-07-31 03:47:32,166 - main_controller - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-31 03:47:32,200 - main_controller - INFO - 📅 启动时间: 2025-07-31 11:47:32
2025-07-31 03:47:32,233 - main_controller - INFO - 🔍 开始扫描微信窗口...
2025-07-31 03:47:32,255 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-31 03:47:32,280 - modules.window_manager - INFO - 🎯 总共找到 0 个微信窗口
2025-07-31 03:47:32,296 - main_controller - ERROR - ❌ 未找到任何微信窗口
2025-07-31 03:47:32,317 - main_controller - ERROR - ❌ 未找到微信窗口，程序退出
